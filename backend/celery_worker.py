from app import create_app
from celery import Celery
from datetime import timedelta
from celery.schedules import crontab

app = create_app()
celery = Celery(
    app.import_name,
    backend=app.config.get('result_backend', 'redis://localhost:6379/0'),
    broker=app.config.get('broker_url', 'redis://localhost:6379/0')
)
celery.conf.update(app.config)

class ContextTask(celery.Task):
    def __call__(self, *args, **kwargs):
        with app.app_context():
            return self.run(*args, **kwargs)

celery.Task = ContextTask

# Import tasks to register them with Celery
from app.tasks import daily_reminders, monthly_reports, export_parking_details_csv

celery.conf.beat_schedule = {
    'daily-reminders': {
        'task': 'app.tasks.daily_reminders',
        'schedule': timedelta(days=1),
    },
    'monthly-reports': {
        'task': 'app.tasks.monthly_reports',
        'schedule': crontab(day_of_month=1, hour=0, minute=0),
    },
}
