from app import create_app, db
from app.models.user import User
from app.models.parking_lot import ParkingLot
from app.models.parking_spot import ParkingSpot

def add_sample_data():
    """Adds sample data if it doesn't already exist."""
    print("Checking for existing sample data...")

    # Check if sample parking lot exists
    sample_lot = ParkingLot.query.filter_by(prime_location_name='Sample Lot').first()

    if sample_lot:
        print("Sample data seems to exist already. Skipping creation.")
        return

    print("Creating sample data...")
    try:
        # 1. Sample Parking Lot
        print("- Creating sample parking lot...")
        lot1 = ParkingLot(prime_location_name='Sample Lot', price=10.0, address='123 Main St', pin_code='12345', number_of_spots=50)
        db.session.add(lot1)
        db.session.commit()
        print(f"  Parking Lot '{lot1.prime_location_name}' created with ID {lot1.id}")

        # 2. Sample Parking Spots for the lot
        print("- Creating sample parking spots...")
        for i in range(1, lot1.number_of_spots + 1):
            spot = ParkingSpot(lot_id=lot1.id)
            db.session.add(spot)
        db.session.commit()
        print(f"  {lot1.number_of_spots} parking spots created for Lot ID {lot1.id}")

        print("Sample data created successfully.")

    except Exception as e:
        db.session.rollback()
        print(f"Error creating sample data: {e}")


def init_db():
    """
    Initializes the database by creating all tables defined in the models
    and ensures a default admin user and sample data exist.
    """
    print("Initializing database...")
    app = create_app()
    with app.app_context():
        print("Creating database tables...")
        # Drop existing tables first for a clean slate (optional, use with caution)
        # print("Dropping existing tables (if any)...")
        # db.drop_all()
        db.create_all()
        print("Tables created.")

        # --- Create Default Admin ---
        admin_username = '<EMAIL>'
        existing_admin = User.query.filter_by(username=admin_username).first()

        if not existing_admin:
            print(f"Creating default admin user: {admin_username}")
            try:
                default_admin = User(
                    username=admin_username, 
                    email=admin_username,  # Use same value for email
                    full_name='Admin', 
                    role='admin'
                )
                default_admin.set_password('admin')
                db.session.add(default_admin)
                db.session.commit()
                print('Default admin user created successfully.')
            except Exception as e:
                db.session.rollback()
                print(f"Error creating default admin user: {e}")
        else:
            print(f"Admin user '{admin_username}' already exists.")

        # --- Add Sample Data ---
        add_sample_data()

        print('Database initialization process completed.')

if __name__ == '__main__':
    init_db()
