from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_mail import Mail
from redis import Redis
import os

db = SQLAlchemy()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message_category = 'info'
mail = Mail()
redis = Redis(host='localhost', port=6379, db=0)

def create_app(config_object=None):
    """
    Application factory function to create and configure the Flask app.
    
    Args:
        config_object: Optional configuration object to use.
    
    Returns:
        The configured Flask application instance.
    """
    app = Flask(__name__, instance_relative_config=True) 

    # --- Configuration ---
    app.config.from_mapping(
        SECRET_KEY=os.urandom(24),
        SQLALCHEMY_DATABASE_URI='sqlite:///vehicle_parking.db',
        SQLALCHEMY_TRACK_MODIFICATIONS=False,
        broker_url='redis://localhost:6379/0',
        result_backend='redis://localhost:6379/0',
        # Email configuration for development (console output)
        MAIL_SERVER='localhost',
        MAIL_PORT=1025,
        MAIL_USE_TLS=False,
        MAIL_USE_SSL=False,
        MAIL_USERNAME=None,
        MAIL_PASSWORD=None,
        MAIL_DEFAULT_SENDER='<EMAIL>',
        MAIL_SUPPRESS_SEND=True  # This will prevent actual email sending in development
    )

    if config_object:
        app.config.from_object(config_object)

    # Ensure instance folder exists
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass 

    # --- Initialize Extensions ---
    db.init_app(app)
    login_manager.init_app(app)
    mail.init_app(app)
    
    # --- Register Blueprints ---
    from .controllers.auth import auth_bp
    from .controllers.admin import admin_bp
    from .controllers.user import user_bp
    from .controllers.api import api_bp
    
    app.register_blueprint(auth_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(api_bp)
    
    # --- User Loader for Flask-Login ---
    from .models.user import User
    
    @login_manager.user_loader
    def load_user(user_id):
        """
        Flask-Login user loader callback.
        Loads a User based on the stored user ID.
        """
        if user_id is not None:
            try:
                return User.query.get(int(user_id))
            except ValueError:
                return None
        return None

    return app
