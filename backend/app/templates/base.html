<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}Park Smart{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
    />
    <!-- Font Awesome CSS -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <!-- Base CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/base.css') }}"
    />
    {# Block for page-specific head content like CSS #} {% block head %}{%
    endblock %}
  </head>
  <body>
    {# Updated Navbar: White background, dark text, added icon #}
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        {# Link brand to the landing page (index) #}
        <a class="navbar-brand fw-bold" href="{{ url_for('auth.index') }}">
          <i class="fas fa-car me-2"></i>Park Smart
        </a>
        {# Add title attribute for accessibility #}
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          title="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            {% if current_user.is_authenticated %} {% if current_user.is_admin
            %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                <i class="bi bi-speedometer2"></i> Dashboard
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('admin.lots') }}">
                <i class="bi bi-p-square"></i> Lots
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('admin.users') }}">
                <i class="bi bi-people"></i> Users
              </a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('user.dashboard') }}">
                <i class="bi bi-speedometer2"></i> Dashboard
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('user.lots') }}">
                <i class="bi bi-p-square"></i> Lots
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('user.history') }}">
                <i class="bi bi-clock-history"></i> Parking History
              </a>
            </li>
            {% endif %} {% endif %}
          </ul>
          <ul class="navbar-nav">
            {% if current_user.is_authenticated %}
            <li class="nav-item">
              {# Removed text-light class #}
              <span class="nav-link">
                <i class="bi bi-person-circle me-1"></i>
                {% if current_user.is_admin %} Admin {% else %} {{
                current_user.full_name }} {% endif %}
              </span>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('auth.logout') }}">
                <i class="bi bi-box-arrow-right"></i> Logout
              </a>
            </li>
            {% else %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('auth.login') }}">
                <i class="bi bi-box-arrow-in-right"></i> Login
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('auth.register') }}">
                <i class="bi bi-person-plus"></i> Register
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      {# Display flashed messages #} {% with messages =
      get_flashed_messages(with_categories=true) %} {% if messages %} {% for
      category, message in messages %}
      <div class="alert alert-{{ category }} alert-dismissible fade show">
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}
    </div>

    <footer class="mt-5 py-3 text-center text-muted bg-light">
      <div class="container">
        <p>Park Smart &copy; 2025. All rights reserved.</p>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </body>
</html>
