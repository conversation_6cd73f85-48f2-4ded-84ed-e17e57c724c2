{% extends "base.html" %} {% block title %}Parking History - Park Smart{%
endblock %} {% block content %}
<div class="container mt-4">
  <h1 class="mb-4">Parking History</h1>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Reservation ID</th>
        <th>Lot Name</th>
        <th>Spot ID</th>
        <th>Parked On</th>
        <th>Left On</th>
        <th>Cost</th>
      </tr>
    </thead>
    <tbody>
      {% for reservation in reservations %}
      <tr>
        <td>{{ reservation.id }}</td>
        <td>{{ reservation.parking_spot.parking_lot.prime_location_name }}</td>
        <td>{{ reservation.spot_id }}</td>
        <td>{{ reservation.parking_timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
        <td>
          {% if reservation.leaving_timestamp %} {{
          reservation.leaving_timestamp.strftime('%Y-%m-%d %H:%M') }} {% else %}
          - {% endif %}
        </td>
        <td>
          {% if reservation.parking_cost %} {{
          "%.2f"|format(reservation.parking_cost) }} {% else %} - {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
