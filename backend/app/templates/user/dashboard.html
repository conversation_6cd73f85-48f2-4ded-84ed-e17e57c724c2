{% extends "base.html" %} {% block title %}User Dashboard - Park Smart{%
endblock %} {% block content %}
<div class="container mt-4">
  <h1 class="mb-4">Welcome, {{ current_user.full_name }}!</h1>

  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      Your Reservations
      <a
        href="{{ url_for('user.export_history') }}"
        class="btn btn-secondary btn-sm"
        >Export as CSV</a
      >
    </div>
    <div class="card-body">
      <a href="{{ url_for('user.lots') }}" class="btn btn-primary mb-3"
        >Book a New Spot</a
      >
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Reservation ID</th>
            <th>Lot Name</th>
            <th>Spot ID</th>
            <th>Parked On</th>
            <th>Left On</th>
            <th>Cost</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for reservation in reservations %}
          <tr>
            <td>{{ reservation.id }}</td>
            <td>
              {{ reservation.parking_spot.parking_lot.prime_location_name }}
            </td>
            <td>{{ reservation.spot_id }}</td>
            <td>
              {{ reservation.parking_timestamp.strftime('%Y-%m-%d %H:%M') }}
            </td>
            <td>
              {% if reservation.leaving_timestamp %} {{
              reservation.leaving_timestamp.strftime('%Y-%m-%d %H:%M') }} {%
              else %} - {% endif %}
            </td>
            <td>
              {% if reservation.parking_cost %} {{
              "%.2f"|format(reservation.parking_cost) }} {% else %} - {% endif
              %}
            </td>
            <td>
              {% if not reservation.leaving_timestamp %}
              <a
                href="{{ url_for('user.release_spot', reservation_id=reservation.id) }}"
                class="btn btn-sm btn-success"
                >Release Spot</a
              >
              {% endif %}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>
{% endblock %}
