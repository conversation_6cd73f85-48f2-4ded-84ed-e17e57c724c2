{% extends 'base.html' %} {% block title %}Register - Park Smart{% endblock %}
{# Add page-specific CSS link #} {% block head %} {{ super() }} {# Includes CSS
from base.html #}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/auth.css') }}"
/>
{% endblock %} {% block content %} {# Two-column layout for registration page #}
<div class="container-fluid auth-page-container">
  {# Full width container #}
  <div
    class="row g-0 align-items-stretch"
    style="min-height: calc(100vh - 70px)"
  >
    {# Adjust 70px based on navbar height #} {# Left Column: Image - ID added,
    inline style removed #}
    <div
      id="signup-image-col"
      class="col-lg-6 d-none d-lg-flex auth-image-container"
    ></div>

    {# Right Column: Form #}
    <div
      class="col-lg-6 d-flex align-items-center justify-content-center auth-form-col"
    >
      <div
        class="auth-form-container p-4 p-md-5 w-100"
        style="max-width: 480px"
      >
        <h2 class="text-center mb-4">Register</h2>
        <form method="POST" action="{{ url_for('auth.register') }}">
          {{ form.hidden_tag() }}

          <div class="mb-3">
            {# Label removed, relying on placeholder #} {{
            form.email(class="form-control", placeholder="Email Address",
            required=true, type="email") }} {# Updated placeholder #} {% if
            form.email.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.email.errors %} {{ error }} {% endfor %}
            </div>
            {% endif %}
          </div>

          <div class="mb-3">
            {# Label removed, relying on placeholder #} {{
            form.full_name(class="form-control", placeholder="Full Name",
            required=true, minlength="2", maxlength="100") }} {# Updated
            placeholder #} {% if form.full_name.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.full_name.errors %} {{ error }} {% endfor %}
            </div>
            {% endif %}
          </div>

          <div class="mb-3">
            {# Label removed, relying on placeholder #} {{
            form.password(class="form-control", placeholder="Password",
            required=true, minlength="6") }} {# Updated placeholder #} {% if
            form.password.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.password.errors %} {{ error }} {% endfor %}
            </div>
            {% endif %}
          </div>

          <div class="mb-3">
            {# Label removed, relying on placeholder #} {{
            form.confirm_password(class="form-control", placeholder="Confirm
            Password", required=true) }} {# Updated placeholder #} {% if
            form.confirm_password.errors %}
            <div class="text-danger small mt-1">
              {% for error in form.confirm_password.errors %} {{ error }} {%
              endfor %}
            </div>
            {% endif %}
          </div>

          {{ form.submit(class="btn btn-primary w-100") }} {# Ensure button is
          full width #}
        </form>
        <div class="auth-links mt-3">
          {# Added margin top #}
          <a href="{{ url_for('auth.login') }}"
            >Already have an account? Login here</a
          >
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
