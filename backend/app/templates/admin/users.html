{% extends "base.html" %} {% block title %}Manage Users - Park Smart{% endblock
%} {% block content %}
<div class="container mt-4">
  <h1 class="mb-4">Manage Users</h1>
  <form method="get" class="mb-3">
    <div class="input-group">
      <input
        type="text"
        name="search"
        class="form-control"
        placeholder="Search by username or full name"
        value="{{ search_term or '' }}"
      />
      <button type="submit" class="btn btn-primary">Search</button>
    </div>
  </form>
  <table class="table table-striped">
    <thead>
      <tr>
        <th>ID</th>
        <th>Username</th>
        <th>Full Name</th>
        <th>Registered On</th>
      </tr>
    </thead>
    <tbody>
      {% for user in users %}
      <tr>
        <td>{{ user.id }}</td>
        <td>{{ user.username }}</td>
        <td>{{ user.full_name }}</td>
        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
