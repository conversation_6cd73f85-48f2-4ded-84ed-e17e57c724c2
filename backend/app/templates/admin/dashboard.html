{% extends "base.html" %} {% block title %}Admin Dashboard - Park Smart{%
endblock %} {% block content %}
<div class="container mt-4">
  <h1 class="mb-4">Admin Dashboard</h1>

  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-white bg-primary">
        <div class="card-body">
          <h5 class="card-title">Total Lots</h5>
          <p class="card-text fs-4">{{ total_lots }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-white bg-info">
        <div class="card-body">
          <h5 class="card-title">Total Spots</h5>
          <p class="card-text fs-4">{{ total_spots }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-white bg-danger">
        <div class="card-body">
          <h5 class="card-title">Occupied Spots</h5>
          <p class="card-text fs-4">{{ occupied_spots }}</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-white bg-success">
        <div class="card-body">
          <h5 class="card-title">Available Spots</h5>
          <p class="card-text fs-4">{{ available_spots }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">Spots per Lot</div>
        <div class="card-body">
          <canvas id="spotsPerLotChart"></canvas>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">Occupancy per Lot</div>
        <div class="card-body">
          <canvas id="occupancyPerLotChart"></canvas>
        </div>
      </div>
    </div>
  </div>

  <div class="card mt-4">
    <div class="card-header">Parking Lots</div>
    <div class="card-body">
      <a href="{{ url_for('admin.add_lot') }}" class="btn btn-primary mb-3"
        >Add New Lot</a
      >
      <table class="table table-striped">
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Address</th>
            <th>Price/Hour</th>
            <th>Spots</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {% for lot in lots %}
          <tr>
            <td>{{ lot.id }}</td>
            <td>{{ lot.prime_location_name }}</td>
            <td>{{ lot.address }}, {{ lot.pin_code }}</td>
            <td>{{ "%.2f"|format(lot.price) }}</td>
            <td>{{ lot.number_of_spots }}</td>
            <td>
              <a
                href="{{ url_for('admin.edit_lot', id=lot.id) }}"
                class="btn btn-sm btn-warning"
                >Edit</a
              >
              <form
                action="{{ url_for('admin.delete_lot', id=lot.id) }}"
                method="post"
                class="d-inline"
              >
                <button
                  type="submit"
                  class="btn btn-sm btn-danger"
                  onclick="return confirm('Are you sure you want to delete this lot?');"
                >
                  Delete
                </button>
              </form>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  const spotsPerLotCtx = document.getElementById('spotsPerLotChart').getContext('2d');
  new Chart(spotsPerLotCtx, {
      type: 'bar',
      data: {
          labels: {{ lot_names|tojson }},
          datasets: [{
              label: '# of Spots',
              data: {{ spots_per_lot|tojson }},
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
          }]
      },
      options: {
          scales: {
              y: {
                  beginAtZero: true
              }
          }
      }
  });

  const occupancyPerLotCtx = document.getElementById('occupancyPerLotChart').getContext('2d');
  new Chart(occupancyPerLotCtx, {
      type: 'pie',
      data: {
          labels: {{ lot_names|tojson }},
          datasets: [{
              label: 'Occupied Spots',
              data: {{ occupied_spots_per_lot|tojson }},
              backgroundColor: [
                  'rgba(255, 99, 132, 0.2)',
                  'rgba(54, 162, 235, 0.2)',
                  'rgba(255, 206, 86, 0.2)',
                  'rgba(75, 192, 192, 0.2)',
                  'rgba(153, 102, 255, 0.2)',
                  'rgba(255, 159, 64, 0.2)'
              ],
              borderColor: [
                  'rgba(255, 99, 132, 1)',
                  'rgba(54, 162, 235, 1)',
                  'rgba(255, 206, 86, 1)',
                  'rgba(75, 192, 192, 1)',
                  'rgba(153, 102, 255, 1)',
                  'rgba(255, 159, 64, 1)'
              ],
              borderWidth: 1
          }]
      }
  });
</script>
{% endblock %}
