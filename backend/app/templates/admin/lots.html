{% extends "base.html" %} {% block title %}Manage Parking Lots - Park Smart{%
endblock %} {% block content %}
<div class="container mt-4">
  <h1 class="mb-4">Manage Parking Lots</h1>
  <a href="{{ url_for('admin.add_lot') }}" class="btn btn-primary mb-3"
    >Add New Lot</a
  >
  <table class="table table-striped">
    <thead>
      <tr>
        <th>ID</th>
        <th>Name</th>
        <th>Address</th>
        <th>Price/Hour</th>
        <th>Spots</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for lot in lots %}
      <tr>
        <td>{{ lot.id }}</td>
        <td>{{ lot.prime_location_name }}</td>
        <td>{{ lot.address }}, {{ lot.pin_code }}</td>
        <td>{{ "%.2f"|format(lot.price) }}</td>
        <td>{{ lot.number_of_spots }}</td>
        <td>
          <a
            href="{{ url_for('admin.edit_lot', id=lot.id) }}"
            class="btn btn-sm btn-warning"
            >Edit</a
          >
          <form
            action="{{ url_for('admin.delete_lot', id=lot.id) }}"
            method="post"
            class="d-inline"
          >
            <button
              type="submit"
              class="btn btn-sm btn-danger"
              onclick="return confirm('Are you sure you want to delete this lot?');"
            >
              Delete
            </button>
          </form>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
