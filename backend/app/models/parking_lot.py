from .. import db

class ParkingLot(db.Model):
    """Represents a parking lot with multiple parking spots."""
    __tablename__ = 'parking_lots'
    
    id = db.Column(db.Integer, primary_key=True)
    prime_location_name = db.Column(db.String(255), nullable=False)
    price = db.Column(db.Float, nullable=False)
    address = db.Column(db.String(255), nullable=False)
    pin_code = db.Column(db.String(20), nullable=False)
    number_of_spots = db.Column(db.Integer, nullable=False)
    
    spots = db.relationship('ParkingSpot', backref='parking_lot', lazy=True, cascade="all, delete-orphan")
    
    def __repr__(self):
        """Provides a developer-friendly representation of the ParkingLot object."""
        return f'<ParkingLot {self.prime_location_name}>'
