from .. import db

class ParkingSpot(db.Model):
    """Represents an individual parking spot in a parking lot."""
    __tablename__ = 'parking_spots'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    lot_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('parking_lots.id'), nullable=False)
    status = db.Column(db.String(1), nullable=False, default='A')  # 'A' for Available, 'O' for Occupied
    
    reservations = db.relationship('Reservation', backref='parking_spot', lazy=True)
    
    def __repr__(self):
        """Provides a developer-friendly representation of the ParkingSpot object."""
        return f'<ParkingSpot {self.id} in Lot {self.lot_id}>'
