from .. import db
from datetime import datetime

class Reservation(db.Model):
    """Represents a reservation of a parking spot by a user."""
    __tablename__ = 'reservations'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    spot_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('parking_spots.id'), nullable=False)
    user_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    parking_timestamp = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    leaving_timestamp = db.Column(db.DateTime)
    parking_cost = db.Column(db.Float)
    
    def __repr__(self):
        """Provides a developer-friendly representation of the Reservation object."""
        return f'<Reservation {self.id} for User {self.user_id}>'
