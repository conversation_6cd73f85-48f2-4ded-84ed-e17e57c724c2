from .. import db
from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

class User(db.Model, UserMixin):
    """Represents a user of the parking application."""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)  
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(100), nullable=False) 
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user') # 'user' or 'admin'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    reservations = db.relationship('Reservation', backref='user', lazy=True, cascade="all, delete-orphan")
    
    def set_password(self, password):
        """Hashes the provided password and stores it."""
        self.password = generate_password_hash(password)
        
    def check_password(self, password):
        """Checks if the provided password matches the stored hash."""
        return check_password_hash(self.password, password)
    
    def __repr__(self):
        """Provides a developer-friendly representation of the User object."""
        return f'<User {self.username}>'

    @property
    def is_admin(self):
        return self.role == 'admin'
