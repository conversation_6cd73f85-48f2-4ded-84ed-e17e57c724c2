from celery_worker import celery
from app.models.user import User
from app.models.reservation import Reservation
from app import create_app, mail
from flask_mail import Message
import csv
from io import String<PERSON>
from datetime import datetime, timedelta
import requests
import os

app = create_app()

def send_google_chat_message(webhook_url, message):
    """Sends a message to Google Chat via webhook."""
    try:
        payload = {
            "text": message
        }
        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()
        print(f"Google Chat message sent successfully")
        return True
    except Exception as e:
        print(f"Failed to send Google Chat message: {e}")
        return False

@celery.task
def daily_reminders():
    """Sends daily reminders to users."""
    with app.app_context():
    users = User.query.filter_by(role='user').all()
    for user in users:
            # Check if user has any recent activity
            recent_reservations = Reservation.query.filter_by(user_id=user.id).filter(
                Reservation.parking_timestamp >= datetime.utcnow() - timedelta(days=7)
            ).count()
            
            if recent_reservations == 0:
                # Send reminder email
                msg = Message(
                    subject="Daily Parking Reminder",
                    recipients=[user.email],
                    body=f"""
Hello {user.username}!

This is your daily reminder from Park Smart. 
You haven't used our parking services recently. 
Why not book a spot today?

Best regards,
Park Smart Team
                    """
                )
                try:
                    mail.send(msg)
                    print(f"Daily reminder sent to {user.email}")
                except Exception as e:
                    print(f"Failed to send daily reminder to {user.email}: {e}")
                
                # Send Google Chat notification if webhook URL is configured
                webhook_url = os.environ.get('GOOGLE_CHAT_WEBHOOK_URL')
                if webhook_url:
                    chat_message = f"""
🚗 **Daily Parking Reminder**

Hello {user.username}!

You haven't used our parking services recently. 
Why not book a spot today?

*Park Smart Team*
                    """
                    send_google_chat_message(webhook_url, chat_message)

@celery.task
def monthly_reports():
    """Generates and sends monthly activity reports to users."""
    with app.app_context():
    users = User.query.filter_by(role='user').all()
    for user in users:
            # Get last month's reservations
            last_month = datetime.utcnow().replace(day=1) - timedelta(days=1)
            start_date = last_month.replace(day=1)
            end_date = datetime.utcnow().replace(day=1)
            
            reservations = Reservation.query.filter_by(user_id=user.id).filter(
                Reservation.parking_timestamp >= start_date,
                Reservation.parking_timestamp < end_date
            ).all()
            
            total_spots = len(reservations)
            total_cost = sum(r.parking_cost or 0 for r in reservations)
            
            # Generate HTML report
            html_content = f"""
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ margin: 20px 0; }}
        .stats {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Monthly Parking Report</h1>
        <p>Report for {start_date.strftime('%B %Y')}</p>
    </div>
    
    <div class="content">
        <h2>Hello {user.username}!</h2>
        <p>Here's your monthly parking activity summary:</p>
        
        <div class="stats">
            <h3>Summary</h3>
            <p><strong>Total Spots Booked:</strong> {total_spots}</p>
            <p><strong>Total Cost:</strong> ${total_cost:.2f}</p>
        </div>
        
        <h3>Detailed Activity</h3>
        <table>
            <tr>
                <th>Date</th>
                <th>Lot</th>
                <th>Spot</th>
                <th>Duration</th>
                <th>Cost</th>
            </tr>
        """
            
            for reservation in reservations:
                duration = "Ongoing"
                if reservation.leaving_timestamp:
                    duration = str(reservation.leaving_timestamp - reservation.parking_timestamp).split('.')[0]
                
                html_content += f"""
            <tr>
                <td>{reservation.parking_timestamp.strftime('%Y-%m-%d %H:%M')}</td>
                <td>{reservation.parking_spot.parking_lot.prime_location_name}</td>
                <td>{reservation.spot_id}</td>
                <td>{duration}</td>
                <td>${reservation.parking_cost:.2f if reservation.parking_cost else 0:.2f}</td>
            </tr>
                """
            
            html_content += """
        </table>
    </div>
    
    <div style="margin-top: 30px; text-align: center; color: #666;">
        <p>Thank you for using Park Smart!</p>
    </div>
</body>
</html>
            """
            
            msg = Message(
                subject=f"Monthly Parking Report - {start_date.strftime('%B %Y')}",
                recipients=[user.email],
                html=html_content
            )
            
            try:
                mail.send(msg)
                print(f"Monthly report sent to {user.email}")
            except Exception as e:
                print(f"Failed to send monthly report to {user.email}: {e}")

@celery.task
def export_parking_details_csv(user_id):
    """Exports parking details for a user as a CSV file and emails it."""
    with app.app_context():
    user = User.query.get(user_id)
    if not user:
        return

    reservations = Reservation.query.filter_by(user_id=user_id).all()

    output = StringIO()
    writer = csv.writer(output)
    writer.writerow(['Reservation ID', 'Lot Name', 'Spot ID', 'Parked On', 'Left On', 'Cost'])

    for reservation in reservations:
        writer.writerow([
            reservation.id,
            reservation.parking_spot.parking_lot.prime_location_name,
            reservation.spot_id,
            reservation.parking_timestamp.strftime('%Y-%m-%d %H:%M'),
            reservation.leaving_timestamp.strftime('%Y-%m-%d %H:%M') if reservation.leaving_timestamp else '-',
            f"{reservation.parking_cost:.2f}" if reservation.parking_cost else '-'
        ])

    csv_data = output.getvalue()
        
        # Create email with CSV attachment
        msg = Message(
            subject="Your Parking History Export",
            recipients=[user.email],
            body=f"""
Hello {user.username}!

Your parking history has been exported as requested.
Please find the CSV file attached to this email.

Total records exported: {len(reservations)}

Best regards,
Park Smart Team
            """
        )
        
        # Attach CSV file
        msg.attach(
            filename=f"parking_history_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.csv",
            content_type="text/csv",
            data=csv_data
        )
        
        try:
            mail.send(msg)
            print(f"CSV export email sent to {user.email}")
            return f"CSV exported successfully for {user.username}"
        except Exception as e:
            print(f"Failed to send CSV export email to {user.email}: {e}")
            return f"Failed to send CSV export: {e}"
