/* Landing Page Specific Styles */

.landing-container .row {
  min-height: calc(100vh - 70px);
}

.landing-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: var(--qm-bg-white);
  padding: 4rem !important;
}

.landing-content h1 {
  color: var(--qm-text-dark);
  font-weight: 700;
  line-height: 1.3;
}

.landing-content h1 span {
  color: var(--qm-primary);
}

.landing-content p.lead {
  color: var(--qm-muted);
  font-size: 1.1rem;
  margin-bottom: 2.5rem !important;
}

.landing-content .btn-primary {
  padding: 0.8rem 1.8rem;
  font-size: 1.1rem;
}

.landing-content .btn-outline-secondary {
  padding: 0.8rem 1.8rem;
  font-size: 1.1rem;
  color: var(--qm-primary);
  border-color: var(--qm-primary);
  font-weight: 500;
}

.landing-content .btn-outline-secondary:hover {
  background-color: var(--qm-primary);
  color: var(--qm-bg-white);
}

.landing-image-container {
  padding: 0 !important;
  height: calc(100vh - 70px);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--qm-bg-light);
}

.landing-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
  .landing-container .row {
    min-height: auto;
  }

  .landing-content {
    padding: 3rem !important;
    text-align: center;
  }

  .landing-content .d-md-flex {
    justify-content: center !important;
  }

  .landing-image-container {
    height: auto;
    max-height: 50vh;
    order: -1;
  }
  .landing-image {
    object-fit: contain;
  }
}
