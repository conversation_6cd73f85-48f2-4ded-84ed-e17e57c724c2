/* User & Admin Dashboard Specific Styles */

/* Main Dashboard Title */
.dashboard-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--qm-text-dark);
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--qm-border-color);
}


/* General Card Styling Enhancements for Dashboards */
.card {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  border-radius: 10px;
  margin-bottom: 2rem;
  background-color: var(--qm-bg-white);
  overflow: hidden;
}

.card-header {
  background-color: var(--qm-bg-light);
  color: var(--qm-text-dark);
  border-bottom: 1px solid var(--qm-border-color);
  border-radius: 10px 10px 0 0;
  padding: 1rem 1.5rem;
  font-weight: 600;
}

/* Chart Card Specific Header Styling */
.card .card-header {
  background-color: var(--qm-bg-white) !important;
  color: var(--qm-text-dark) !important;
  border-bottom: 1px solid var(--qm-border-color);
  border-radius: 10px 10px 0 0;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
  position: relative;
}

/* Ensure chart canvas has space from header AND override Bootstrap flex */
.card .card-body {
    padding-top: 1rem;
    display: block;
    flex: none;
    box-sizing: border-box;
}

/* Chart Wrapper to contain canvas */
.chart-wrapper {
  position: relative;
  height: 300px;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Chart Containers (Canvas) */
.card canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
}


/* --- Other Styles (Stat Boxes, Tables, etc.) --- */

/* Welcome Alert */
.alert-info {
  background-color: #e0f2fe;
  color: #075985;
  border-color: #bae6fd;
  border-left: 4px solid var(--qm-primary);
  border-radius: 8px;
}

/* Stat Boxes inside Cards */
.stat-box {
  background-color: var(--qm-bg-light);
  border: 1px solid var(--qm-border-color);
  border-radius: 8px;
  padding: 1.5rem 1rem;
  text-align: center;
  transition: background-color 0.2s ease-in-out, transform 0.2s ease-in-out;
  margin-bottom: 1rem;
}
.stat-box:hover {
  background-color: #e9ecef;
  transform: translateY(-3px);
}
.stat-box h3 {
  color: var(--qm-primary);
  margin-bottom: 0.3rem;
  font-weight: 700;
  font-size: 2rem;
}
.stat-box p {
  color: var(--qm-muted);
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* Table Styling */
.table {
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  width: 100%;
}
.table thead th {
  background-color: var(--qm-bg-light);
  border-bottom: 2px solid var(--qm-border-color);
  font-weight: 600;
  color: var(--qm-text-dark);
  vertical-align: middle;
  text-align: left;
  padding: 0.9rem 1rem;
  font-size: 1rem;
}
.table tbody tr:hover {
  background-color: #f1f3f5;
}
.table td,
.table th {
  padding: 0.9rem 1rem;
  vertical-align: middle;
  text-align: left;
  border-bottom: 1px solid var(--qm-border-color);
}
.table tbody tr:last-child td {
  border-bottom: none;
}
.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--qm-border-color);
}

/* Buttons within tables/cards */
.table .btn {
  padding: 0.3rem 0.7rem;
  font-size: 0.85rem;
  margin-right: 0.3rem;
}
.btn-outline-secondary {
  color: var(--qm-muted);
  border-color: var(--qm-muted);
}
.btn-outline-secondary:hover {
  background-color: var(--qm-muted);
  color: white;
}
.btn-success {
  background-color: var(--qm-success);
  color: white;
}
.btn-success:hover {
  background-color: #157347;
  color: white;
}

/* Text muted */
.text-muted {
  color: var(--qm-muted) !important;
}

/* Refined Badge Styling */
.badge {
  padding: 0.4em 0.7em;
  font-size: 1rem;
  font-weight: 500;
}
.badge.bg-light {
  border: 1px solid var(--qm-border-color);
}

/* Styling for count displays in dashboard chapter list */
.count-display {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 1em;
  font-weight: 500;
  line-height: 1;
  color: var(--qm-text-dark);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem;
  border: 1px solid var(--qm-border-color);
  background-color: var(--qm-bg-light);
}
