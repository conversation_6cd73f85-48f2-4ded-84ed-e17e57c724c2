@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@400;500;700&display=swap");

/* Define Color Scheme Variables */
:root {
  --qm-bg-white: #ffffff;
  --qm-text-dark: #343a40;
  --qm-primary: #0d6efd;
  --qm-secondary: #ffa500;
  --qm-success: #198754;
  --qm-danger: #dc3545;
  --qm-info: #0dcaf0;
  --qm-warning: #ffc107;
  --qm-muted: #6c757d;
  --qm-border-color: #dee2e6;
}

/* Base styles */
body {
  font-family: "Inter", "Roboto", sans-serif;
  background-color: var(--qm-bg-white);
  color: var(--qm-text-dark);
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Make main content area grow */
.container.mt-4 {
  flex-grow: 1;
}

/* Updated Navbar Styles */
.navbar {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.navbar-brand {
  color: var(--qm-primary) !important;
  font-weight: 600;
  font-size: 2rem;
}
.navbar-brand .fa-graduation-cap {
  color: var(--qm-primary);
}

.navbar .nav-link {
  color: var(--qm-text-dark);
  font-weight: 500;
  font-size: 1.2rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.navbar .nav-link:hover,
.navbar .nav-link.active {
  color: var(--qm-primary);
}

/* Adjust logged-in user text color */
.navbar .navbar-nav .nav-link.text-light {
  color: var(--qm-text-dark) !important;
}

/* Basic Button Styling */
.btn {
  border-radius: 0.8rem;
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--qm-primary);
  color: var(--qm-bg-white);
  box-shadow: 0 3px 8px rgba(13, 110, 253, 0.3);
}

.btn-primary:hover {
  background-color: #0b5ed7;
  color: var(--qm-bg-white);
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(13, 110, 253, 0.4);
}

.btn-secondary {
  background-color: var(--qm-bg-white);
  color: var(--qm-primary);
  border: 1px solid var(--qm-primary);
}

.btn-secondary:hover {
  background-color: var(--qm-bg-light);
  color: var(--qm-primary);
  border-color: var(--qm-primary);
}

/* Footer basic style */
footer.bg-light {
  background-color: var(--qm-bg-white) !important;
  border-top: 1px solid var(--qm-border-color);
  color: var(--qm-muted);
}
