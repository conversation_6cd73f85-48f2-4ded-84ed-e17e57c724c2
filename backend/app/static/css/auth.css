/* Styles for the two-column layout */
.auth-page-container {
  overflow: hidden;
}

.auth-image-container {
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  background-color: var(--qm-bg-light);
}

/* Specific background images */
#login-image-col {
  background-image: url("../images/signup.png");
}
#signup-image-col {
  background-image: url("../images/signup.png");
}

.auth-form-col {
  background-color: var(--qm-bg-white);
}

.auth-form-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--qm-text-dark);
  font-weight: 600;
}

/* Form Styling */
.auth-form-container .form-label {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.auth-form-container .form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--qm-border-color);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.auth-form-container .form-control:focus {
  border-color: var(--qm-primary);
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.2);
}

/* Checkbox styling (for admin login) */
.auth-form-container .form-check-input {
  border-radius: 4px;
  border-color: var(--qm-border-color);
}
.auth-form-container .form-check-input:checked {
  background-color: var(--qm-primary);
  border-color: var(--qm-primary);
}
.auth-form-container .form-check-label {
  color: var(--qm-muted);
  font-size: 0.9rem;
}

/* Button Styling */
.auth-form-container .btn-primary {
  width: 100%;
  padding: 0.8rem;
  font-size: 1.05rem;
  margin-top: 1.5rem;
}

/* Links below form */
.auth-links {
  text-align: center;
  margin-top: 1.5rem;
}

.auth-links a {
  color: var(--qm-primary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.auth-links a:hover {
  color: #0b5ed7;
  text-decoration: underline;
}

.auth-links span {
  color: var(--qm-muted);
  margin: 0 0.5rem;
}
