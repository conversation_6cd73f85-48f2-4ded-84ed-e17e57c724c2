from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from .. import db
from ..models.user import User
from ..models.parking_lot import ParkingLot
from ..models.parking_spot import ParkingSpot
from ..models.reservation import Reservation

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/dashboard')
@login_required
def dashboard():
    """Admin dashboard."""
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('user.dashboard'))
    
    # Get statistics
    total_users = User.query.filter_by(role='user').count()
    total_lots = ParkingLot.query.count()
    total_spots = ParkingSpot.query.count()
    active_reservations = Reservation.query.filter_by(leaving_timestamp=None).count()
    
    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_lots=total_lots,
                         total_spots=total_spots,
                         active_reservations=active_reservations)

@admin_bp.route('/lots')
@login_required
def lots():
    """Manage parking lots."""
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('user.dashboard'))
    
    lots = ParkingLot.query.all()
    return render_template('admin/lots.html', lots=lots)

@admin_bp.route('/users')
@login_required
def users():
    """Manage users."""
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('user.dashboard'))
    
    users = User.query.filter_by(role='user').all()
    return render_template('admin/users.html', users=users) 