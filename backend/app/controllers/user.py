from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from .. import db
from ..models.parking_lot import ParkingLot
from ..models.parking_spot import ParkingSpot
from ..models.reservation import Reservation
from datetime import datetime

user_bp = Blueprint('user', __name__)

@user_bp.route('/dashboard')
@login_required
def dashboard():
    """User dashboard."""
    if current_user.is_admin:
        return redirect(url_for('admin.dashboard'))
    
    # Get user's active reservation
    active_reservation = Reservation.query.filter_by(
        user_id=current_user.id,
        leaving_timestamp=None
    ).first()
    
    # Get user's reservation history
    reservations = Reservation.query.filter_by(
        user_id=current_user.id
    ).order_by(Reservation.parking_timestamp.desc()).limit(5).all()
    
    return render_template('user/dashboard.html',
                         active_reservation=active_reservation,
                         reservations=reservations)

@user_bp.route('/lots')
@login_required
def lots():
    """View available parking lots."""
    if current_user.is_admin:
        return redirect(url_for('admin.lots'))
    
    lots = ParkingLot.query.all()
    return render_template('user/lots.html', lots=lots)

@user_bp.route('/history')
@login_required
def history():
    """View parking history."""
    if current_user.is_admin:
        return redirect(url_for('admin.dashboard'))
    
    reservations = Reservation.query.filter_by(
        user_id=current_user.id
    ).order_by(Reservation.parking_timestamp.desc()).all()
    
    return render_template('user/history.html', reservations=reservations) 