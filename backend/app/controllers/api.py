from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from .. import db
from ..models.user import User
from ..models.parking_lot import ParkingLot
from ..models.parking_spot import ParkingSpot
from ..models.reservation import Reservation
from datetime import datetime

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/login', methods=['POST'])
def login():
    """API endpoint for user login."""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    user = User.query.filter_by(username=username).first()
    
    if user and user.check_password(password):
        # In a real app, you'd generate a JWT token here
        return jsonify({
            'success': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.full_name,
                'role': user.role
            },
            'token': 'dummy-token'  # Replace with real JWT token
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Invalid username or password'
        }), 401

@api_bp.route('/register', methods=['POST'])
def register():
    """API endpoint for user registration."""
    data = request.get_json()
    
    # Check if user already exists
    existing_user = User.query.filter_by(username=data.get('email')).first()
    if existing_user:
        return jsonify({
            'success': False,
            'message': 'User already exists'
        }), 400
    
    # Create new user
    new_user = User(
        username=data.get('email'),
        email=data.get('email'),
        full_name=data.get('full_name'),
        role='user'
    )
    new_user.set_password(data.get('password'))
    
    try:
        db.session.add(new_user)
        db.session.commit()
        return jsonify({
            'success': True,
            'message': 'Registration successful'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@api_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """API endpoint for user logout."""
    return jsonify({'success': True})

@api_bp.route('/parking-lots', methods=['GET'])
@login_required
def get_parking_lots():
    """Get all parking lots."""
    lots = ParkingLot.query.all()
    return jsonify([{
        'id': lot.id,
        'prime_location_name': lot.prime_location_name,
        'price': lot.price,
        'address': lot.address,
        'pin_code': lot.pin_code,
        'number_of_spots': lot.number_of_spots,
        'available_spots': len([spot for spot in lot.spots if spot.status == 'A'])
    } for lot in lots])

@api_bp.route('/parking-lots/<int:lot_id>/book', methods=['POST'])
@login_required
def book_spot(lot_id):
    """Book a parking spot in the specified lot."""
    lot = ParkingLot.query.get_or_404(lot_id)
    available_spot = ParkingSpot.query.filter_by(lot_id=lot.id, status='A').first()
    
    if not available_spot:
        return jsonify({
            'success': False,
            'message': 'No available spots in this parking lot'
        }), 400
    
    try:
        available_spot.status = 'O'
        reservation = Reservation(
            spot_id=available_spot.id,
            user_id=current_user.id,
        )
        db.session.add(reservation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Successfully booked spot {available_spot.id} in {lot.prime_location_name}'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@api_bp.route('/reservations', methods=['GET'])
@login_required
def get_reservations():
    """Get user's reservations."""
    reservations = Reservation.query.filter_by(user_id=current_user.id).all()
    return jsonify([{
        'id': r.id,
        'spot_id': r.spot_id,
        'parking_timestamp': r.parking_timestamp.isoformat() if r.parking_timestamp else None,
        'leaving_timestamp': r.leaving_timestamp.isoformat() if r.leaving_timestamp else None,
        'parking_cost': r.parking_cost,
        'parking_spot': {
            'id': r.parking_spot.id,
            'parking_lot': {
                'id': r.parking_spot.parking_lot.id,
                'prime_location_name': r.parking_spot.parking_lot.prime_location_name,
                'price': r.parking_spot.parking_lot.price
            }
        } if r.parking_spot else None
    } for r in reservations])

@api_bp.route('/reservations/<int:reservation_id>/release', methods=['POST'])
@login_required
def release_spot(reservation_id):
    """Release a booked parking spot."""
    reservation = Reservation.query.get_or_404(reservation_id)
    
    if reservation.user_id != current_user.id:
        return jsonify({
            'success': False,
            'message': 'You are not authorized to perform this action'
        }), 403
    
    if reservation.leaving_timestamp:
        return jsonify({
            'success': False,
            'message': 'This spot has already been released'
        }), 400
    
    try:
        spot = ParkingSpot.query.get(reservation.spot_id)
        spot.status = 'A'
        reservation.leaving_timestamp = datetime.utcnow()
        
        time_parked = reservation.leaving_timestamp - reservation.parking_timestamp
        hours_parked = time_parked.total_seconds() / 3600
        reservation.parking_cost = hours_parked * spot.parking_lot.price
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Successfully released spot {spot.id}'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@api_bp.route('/export-history', methods=['POST'])
@login_required
def export_history():
    """Trigger CSV export for user."""
    from ..tasks import export_parking_details_csv
    export_parking_details_csv.delay(current_user.id)
    
    return jsonify({
        'success': True,
        'message': 'Your parking history is being exported. You will receive an email with the CSV file shortly.'
    })

@api_bp.route('/users', methods=['GET'])
@login_required
def get_users():
    """Get all users (admin only)."""
    if current_user.role != 'admin':
        return jsonify({
            'success': False,
            'message': 'Admin access required'
        }), 403
    
    users = User.query.all()
    return jsonify([{
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'full_name': user.full_name,
        'role': user.role,
        'created_at': user.created_at.isoformat() if user.created_at else None
    } for user in users])
