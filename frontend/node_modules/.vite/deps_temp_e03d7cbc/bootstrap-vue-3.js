import {
  <PERSON><PERSON>,
  Carousel,
  Collapse,
  Dropdown,
  <PERSON><PERSON><PERSON>,
  Popover,
  Tooltip
} from "./chunk-FJMSR4QJ.js";
import {
  Comment,
  Fragment,
  Teleport,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  guardReactiveProps,
  h,
  inject,
  isReactive,
  isRef,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onActivated,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  openBlock,
  provide,
  reactive,
  readonly,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDynamicComponent,
  shallowRef,
  toDisplayString,
  toRef,
  unref,
  useAttrs,
  useSlots,
  vModelCheckbox,
  vModelRadio,
  vModelSelect,
  vShow,
  watch,
  watchEffect,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-AJ6RA5K3.js";
import "./chunk-SSYGV25P.js";

// node_modules/bootstrap-vue-3/dist/bootstrap-vue-3.es.js
var El = Object.defineProperty;
var Dl = (e, t, a) => t in e ? El(e, t, { enumerable: true, configurable: true, writable: true, value: a }) : e[t] = a;
var be = (e, t, a) => (Dl(e, typeof t != "symbol" ? t + "" : t, a), a);
var on = (e, t) => {
  var l;
  const a = shallowRef();
  return watchEffect(
    () => {
      a.value = e();
    },
    {
      ...t,
      flush: (l = t == null ? void 0 : t.flush) != null ? l : "sync"
    }
  ), readonly(a);
};
var ot = (e) => computed(() => e.value ? `justify-content-${e.value}` : "");
var Xe = class _Xe {
  constructor(t, a = {}) {
    be(this, "cancelable", true);
    be(this, "componentId", null);
    be(this, "_defaultPrevented", false);
    be(this, "eventType", "");
    be(this, "nativeEvent", null);
    be(this, "_preventDefault");
    be(this, "relatedTarget", null);
    be(this, "target", null);
    if (!t)
      throw new TypeError(
        `Failed to construct '${this.constructor.name}'. 1 argument required, ${arguments.length} given.`
      );
    Object.assign(this, _Xe.Defaults, a, { eventType: t }), this._preventDefault = function() {
      this.cancelable && (this.defaultPrevented = true);
    };
  }
  get defaultPrevented() {
    return this._defaultPrevented;
  }
  set defaultPrevented(t) {
    this._defaultPrevented = t;
  }
  get preventDefault() {
    return this._preventDefault;
  }
  set preventDefault(t) {
    this._preventDefault = t;
  }
  static get Defaults() {
    return {
      cancelable: true,
      componentId: null,
      eventType: "",
      nativeEvent: null,
      relatedTarget: null,
      target: null
    };
  }
};
var sn = class extends Xe {
  constructor(a, l = {}) {
    super(a, l);
    be(this, "trigger", null);
    Object.assign(this, Xe.Defaults, l, { eventType: a });
  }
  static get Defaults() {
    return {
      ...super.Defaults,
      trigger: null
    };
  }
};
var Pt = (e) => e !== null && typeof e == "object";
var Pa = (e) => /^[0-9]*\.?[0-9]+$/.test(String(e));
var rn = (e) => Object.prototype.toString.call(e) === "[object Object]";
var Ne = (e) => e === null;
var Oa = /_/g;
var La = /([a-z])([A-Z])/g;
var un = /(\s|^)(\w)/g;
var dn = /(\s|^)(\w)/;
var dt = /\s+/;
var cn = /^#/;
var fn = /^#[A-Za-z]+[\w\-:.]*$/;
var vn = /-u-.+/;
var mt = (e, t = 2) => typeof e == "string" ? e : e == null ? "" : Array.isArray(e) || rn(e) && e.toString === Object.prototype.toString ? JSON.stringify(e, null, t) : String(e);
var ta = (e) => e.replace(Oa, " ").replace(La, (t, a, l) => `${a} ${l}`).replace(dn, (t, a, l) => a + l.toUpperCase());
var aa = (e) => e.replace(Oa, " ").replace(La, (t, a, l) => `${a} ${l}`).replace(un, (t, a, l) => a + l.toUpperCase());
var mn = (e) => {
  const t = e.trim();
  return t.charAt(0).toUpperCase() + t.slice(1);
};
var At = (e) => `\\${e}`;
var bn = (e) => {
  const t = mt(e), { length: a } = t, l = t.charCodeAt(0);
  return t.split("").reduce((n, u, f) => {
    const c = t.charCodeAt(f);
    return c === 0 ? `${n}�` : c === 127 || c >= 1 && c <= 31 || f === 0 && c >= 48 && c <= 57 || f === 1 && c >= 48 && c <= 57 && l === 45 ? n + At(`${c.toString(16)} `) : f === 0 && c === 45 && a === 1 ? n + At(u) : c >= 128 || c === 45 || c === 95 || c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 ? n + u : n + At(u);
  }, "");
};
var Wt = typeof window < "u";
var gn = typeof document < "u";
var pn = typeof navigator < "u";
var za = Wt && gn && pn;
var la = Wt ? window : {};
var hn = (() => {
  let e = false;
  if (za)
    try {
      const t = {
        get passive() {
          e = true;
        }
      };
      la.addEventListener("test", t, t), la.removeEventListener("test", t, t);
    } catch {
      e = false;
    }
  return e;
})();
var Na = typeof window < "u";
var Ea = typeof document < "u";
var yn = typeof Element < "u";
var Da = typeof navigator < "u";
var ht = Na && Ea && Da;
var je = Na ? window : {};
var yt = Ea ? document : {};
var Ha = Da ? navigator : {};
var Ra = (Ha.userAgent || "").toLowerCase();
Ra.indexOf("jsdom") > 0;
/msie|trident/.test(Ra);
(() => {
  let e = false;
  if (ht)
    try {
      const t = {
        get passive() {
          return e = true, e;
        }
      };
      je.addEventListener("test", t, t), je.removeEventListener("test", t, t);
    } catch {
      e = false;
    }
  return e;
})();
ht && ("ontouchstart" in yt.documentElement || Ha.maxTouchPoints > 0);
ht && Boolean(je.PointerEvent || je.MSPointerEvent);
ht && "IntersectionObserver" in je && "IntersectionObserverEntry" in je && "intersectionRatio" in je.IntersectionObserverEntry.prototype;
var xe = yn ? Element.prototype : void 0;
var Bn = (xe == null ? void 0 : xe.matches) || (xe == null ? void 0 : xe.msMatchesSelector) || (xe == null ? void 0 : xe.webkitMatchesSelector);
var ze = (e) => !!(e && e.nodeType === Node.ELEMENT_NODE);
var $n = (e) => ze(e) ? e.getBoundingClientRect() : null;
var kn = (e = []) => {
  const { activeElement: t } = document;
  return t && !e.some((a) => a === t) ? t : null;
};
var Cn = (e) => ze(e) && e === kn();
var Sn = (e, t = {}) => {
  try {
    e.focus(t);
  } catch (a) {
    console.error(a);
  }
  return Cn(e);
};
var wn = (e, t) => t && ze(e) && e.getAttribute(t) || null;
var Tn = (e) => {
  if (wn(e, "display") === "none")
    return false;
  const t = $n(e);
  return !!(t && t.height > 0 && t.width > 0);
};
var Ve = (e, t) => !e || e(t).filter((a) => a.type !== Comment).length < 1;
var qa = (e, t) => (ze(t) ? t : yt).querySelector(e) || null;
var _n = (e, t) => Array.from([(ze(t) ? t : yt).querySelectorAll(e)]);
var Kt = (e, t) => t && ze(e) ? e.getAttribute(t) : null;
var Vn = (e) => yt.getElementById(/^#/.test(e) ? e.slice(1) : e) || null;
var An = (e, t, a) => {
  t && ze(e) && e.setAttribute(t, a);
};
var xn = (e, t) => {
  t && ze(e) && e.removeAttribute(t);
};
var In = (e, t) => mt(e).toLowerCase() === mt(t).toLowerCase();
var it = Wt ? window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.msRequestAnimationFrame || window.oRequestAnimationFrame || ((e) => setTimeout(e, 16)) : (e) => setTimeout(e, 0);
var Ma = (e, t) => ze(e) ? Bn.call(e, t) : false;
var Fn = (xe == null ? void 0 : xe.closest) || function(e) {
  let t = this;
  if (!t)
    return null;
  do {
    if (Ma(t, e))
      return t;
    t = t.parentElement || t.parentNode;
  } while (t !== null && t.nodeType === Node.ELEMENT_NODE);
  return null;
};
var na = (e, t, a = false) => {
  if (!ze(t))
    return null;
  const l = Fn.call(t, e);
  return a ? l : l === t ? null : l;
};
var Bt = (e, t, a) => t.concat(["sm", "md", "lg", "xl", "xxl"]).reduce((l, n) => (l[e ? `${e}${n.charAt(0).toUpperCase() + n.slice(1)}` : n] = a, l), /* @__PURE__ */ Object.create(null));
var ja = (e, t, a, l = a) => Object.keys(t).reduce((n, u) => (e[u] && n.push(
  [l, u.replace(a, ""), e[u]].filter((f) => f && typeof f != "boolean").join("-").toLowerCase()
), n), []);
var Ee = (e = "") => `__BVID__${Math.random().toString().slice(2, 8)}___BV_${e}__`;
var $t = (e, t) => e === true || e === "true" || e === "" ? "true" : e === "grammar" || e === "spelling" ? e : t === false ? "true" : e === false || e === "false" ? "false" : void 0;
var xt = (e) => !!e && typeof e == "object" && e.constructor === Object;
var Ot = (e, t, a = true) => {
  const l = e instanceof Date && typeof e.getMonth == "function" ? new Date(e.getTime()) : Object.assign({}, e);
  return xt(e) && xt(t) && Object.keys(t).forEach((n) => {
    xt(t[n]) ? n in e ? l[n] = Ot(e[n], t[n], a) : Object.assign(l, { [n]: t[n] }) : Array.isArray(t[n]) && Array.isArray(e[n]) ? Object.assign(l, {
      [n]: a ? e[n].concat(
        t[n].filter((u) => !e[n].includes(u))
      ) : t[n]
    }) : Object.assign(l, { [n]: t[n] });
  }), l;
};
var Fe = (e, t = {}, a = {}) => {
  const l = [e];
  let n;
  for (let u = 0; u < l.length && !n; u++) {
    const f = l[u];
    n = a[f];
  }
  return n && typeof n == "function" ? n(t) : n;
};
var Le = (e, t = NaN) => Number.isInteger(e) ? e : t;
var Qe = (e, t = NaN) => {
  const a = Number.parseInt(e, 10);
  return Number.isNaN(a) ? t : a;
};
var tt = (e, t = NaN) => {
  const a = Number.parseFloat(e.toString());
  return Number.isNaN(a) ? t : a;
};
var kt = (e, t) => Object.keys(e).filter((a) => !t.includes(a)).reduce((a, l) => ({ ...a, [l]: e[l] }), {});
var bt = (e) => Array.isArray(e) ? e.map((t) => bt(t)) : e instanceof Date ? new Date(e.getTime()) : e && typeof e == "object" ? Object.getOwnPropertyNames(e).reduce((t, a) => {
  var l;
  return Object.defineProperty(t, a, (l = Object.getOwnPropertyDescriptor(e, a)) != null ? l : {}), t[a] = bt(e[a]), t;
}, Object.create(Object.getPrototypeOf(e))) : e;
var Lt = (e) => new Promise((t) => t(bt(e)));
var oa = (e, t) => t + (e ? mn(e) : "");
var Xt = (e, t) => (Array.isArray(t) ? t.slice() : Object.keys(t)).reduce(
  (a, l) => (a[l] = e[l], a),
  {}
);
var Pn = (e) => typeof e == "boolean" ? e : e === "" ? true : e === "true";
var at = (e) => !!(e.href || e.to);
function r(e) {
  return on(() => e.value === void 0 ? void 0 : Pn(e.value));
}
var Ga = Symbol();
var Ua = {
  items: reactive([]),
  reset() {
    this.items = reactive([]);
  }
};
var On = (e) => {
  e.provide(Ga, Ua);
};
var Ln = () => {
  const e = inject(Ga);
  return e || Ua;
};
var Be = (e, t, a) => {
  onMounted(() => {
    var l;
    (l = e == null ? void 0 : e.value) == null || l.addEventListener(t, a);
  }), onBeforeUnmount(() => {
    var l;
    (l = e == null ? void 0 : e.value) == null || l.removeEventListener(t, a);
  });
};
var Wa = (e) => computed(() => ({
  "form-check": e.plain === false && e.button === false,
  "form-check-inline": e.inline === true,
  "form-switch": e.switch === true,
  [`form-control-${e.size}`]: e.size !== void 0 && e.size !== "md"
}));
var Ka = (e) => computed(() => ({
  "form-check-input": e.plain === false && e.button === false,
  "is-valid": e.state === true,
  "is-invalid": e.state === false,
  "btn-check": e.button === true
}));
var Xa = (e) => computed(() => ({
  "form-check-label": e.plain === false && e.button === false,
  btn: e.button === true,
  [`btn-${e.buttonVariant}`]: e.button === true && e.buttonVariant !== void 0,
  [`btn-${e.size}`]: e.button && e.size && e.size !== "md"
}));
var Ja = (e) => computed(() => ({
  "aria-invalid": $t(e.ariaInvalid, e.state),
  "aria-required": e.required === true ? true : void 0
}));
var Ya = (e) => computed(() => ({
  "was-validated": e.validated === true,
  "btn-group": e.buttons === true && e.stacked === false,
  "btn-group-vertical": e.stacked === true,
  [`btn-group-${e.size}`]: e.size !== void 0
}));
var gt = (e, t, a) => e.reduce(
  (l, n) => n.type.toString() === "Symbol(Fragment)" ? l.concat(n.children) : l.concat([n]),
  []
).filter((l) => l.type.__name === t || l.type.name === t).map((l) => {
  const n = (l.children.default ? l.children.default() : []).find(
    (u) => u.type.toString() === "Symbol(Text)"
  );
  return {
    props: {
      disabled: a,
      ...l.props
    },
    text: n ? n.children : ""
  };
});
var Za = (e, t) => typeof e == "string" ? {
  props: {
    value: e,
    disabled: t.disabled
  },
  text: e
} : {
  props: {
    value: e[t.valueField],
    disabled: t.disabled || e[t.disabledField],
    ...e.props
  },
  text: e[t.textField],
  html: e[t.htmlField]
};
var Qa = (e, t, a, l, n) => ({
  ...e,
  props: {
    "button-variant": a.buttonVariant,
    form: a.form,
    name: l.value,
    id: `${n.value}_option_${t}`,
    button: a.buttons,
    state: a.state,
    plain: a.plain,
    size: a.size,
    inline: !a.stacked,
    required: a.required,
    ...e.props
  }
});
var ke = (e, t) => computed(() => (e == null ? void 0 : e.value) || Ee(t));
var el = {
  ariaInvalid: {
    type: [Boolean, String],
    default: void 0
  },
  autocomplete: { type: String, required: false },
  autofocus: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
  form: { type: String, required: false },
  formatter: { type: Function, required: false },
  id: { type: String, required: false },
  lazy: { type: Boolean, default: false },
  lazyFormatter: { type: Boolean, default: false },
  list: { type: String, required: false },
  modelValue: { type: [String, Number], default: "" },
  name: { type: String, required: false },
  number: { type: Boolean, default: false },
  placeholder: { type: String, required: false },
  plaintext: { type: Boolean, default: false },
  readonly: { type: Boolean, default: false },
  required: { type: Boolean, default: false },
  size: { type: String, required: false },
  state: { type: Boolean, default: null },
  trim: { type: Boolean, default: false }
};
var tl = (e, t) => {
  const a = ref();
  let l = null, n = true;
  const u = ke(toRef(e, "id"), "input"), f = (b, k, y = false) => (b = String(b), typeof e.formatter == "function" && (!e.lazyFormatter || y) ? (n = false, e.formatter(b, k)) : b), c = (b) => e.trim ? b.trim() : e.number ? Number.parseFloat(b) : b, v = () => {
    nextTick(() => {
      var b;
      e.autofocus && ((b = a.value) == null || b.focus());
    });
  };
  onMounted(v), onMounted(() => {
    a.value && (a.value.value = e.modelValue);
  }), onActivated(v);
  const p = computed(
    () => {
      var b;
      return $t(e.ariaInvalid, (b = e.state) != null ? b : void 0);
    }
  ), B = (b) => {
    const { value: k } = b.target, y = f(k, b);
    if (y === false || b.defaultPrevented) {
      b.preventDefault();
      return;
    }
    if (e.lazy)
      return;
    const C = c(y);
    e.modelValue !== C && (l = k, t("update:modelValue", C)), t("input", y);
  }, m = (b) => {
    const { value: k } = b.target, y = f(k, b);
    if (y === false || b.defaultPrevented) {
      b.preventDefault();
      return;
    }
    if (!e.lazy)
      return;
    l = k, t("update:modelValue", y);
    const C = c(y);
    e.modelValue !== C && t("change", y);
  }, $ = (b) => {
    if (t("blur", b), !e.lazy && !e.lazyFormatter)
      return;
    const { value: k } = b.target, y = f(k, b, true);
    l = k, t("update:modelValue", y);
  }, V = () => {
    var b;
    e.disabled || (b = a.value) == null || b.focus();
  }, T = () => {
    var b;
    e.disabled || (b = a.value) == null || b.blur();
  };
  return watch(
    () => e.modelValue,
    (b) => {
      !a.value || (a.value.value = l && n ? l : b, l = null, n = true);
    }
  ), {
    input: a,
    computedId: u,
    computedAriaInvalid: p,
    onInput: B,
    onChange: m,
    onBlur: $,
    focus: V,
    blur: T
  };
};
var Ke = (e, t) => {
  if (!e)
    return e;
  if (t in e)
    return e[t];
  const a = t.split(".");
  return Ke(e[a[0]], a.splice(1).join("."));
};
var It = (e, t = null, a, l) => {
  if (Object.prototype.toString.call(e) === "[object Object]") {
    const n = Ke(e, l.valueField), u = Ke(e, l.textField), f = Ke(e, l.htmlField), c = Ke(e, l.disabledField), v = e[l.optionsField] || null;
    return v !== null ? {
      label: String(Ke(e, l.labelField) || u),
      options: Jt(v, a, l)
    } : {
      value: typeof n > "u" ? t || u : n,
      text: String(typeof u > "u" ? t : u),
      html: f,
      disabled: Boolean(c)
    };
  }
  return {
    value: t || e,
    text: String(e),
    disabled: false
  };
};
var Jt = (e, t, a) => Array.isArray(e) ? e.map((l) => It(l, null, t, a)) : Object.prototype.toString.call(e) === "[object Object]" ? (console.warn(
  `[BootstrapVue warn]: ${t} - Setting prop "options" to an object is deprecated. Use the array format instead.`
), Object.keys(e).map((l) => {
  const n = e[l];
  switch (typeof n) {
    case "object":
      return It(n.text, String(n.value), t, a);
    default:
      return It(n, String(l), t, a);
  }
})) : [];
var zn = ["id"];
var al = Symbol();
var Nn = defineComponent({
  __name: "BAccordion",
  props: {
    flush: { default: false },
    free: { default: false },
    id: null
  },
  setup(e) {
    const t = e, a = ke(toRef(t, "id"), "accordion"), l = r(toRef(t, "flush")), n = r(toRef(t, "free")), u = computed(() => ({
      "accordion-flush": l.value
    }));
    return n.value || provide(al, a.value), (f, c) => (openBlock(), createElementBlock("div", {
      id: unref(a),
      class: normalizeClass(["accordion", unref(u)])
    }, [
      renderSlot(f.$slots, "default")
    ], 10, zn));
  }
});
var ll = defineComponent({
  __name: "BCollapse",
  props: {
    accordion: null,
    id: { default: Ee() },
    modelValue: { default: false },
    tag: { default: "div" },
    toggle: { default: false },
    visible: { default: false },
    isNav: { default: false }
  },
  emits: ["update:modelValue", "show", "shown", "hide", "hidden"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "modelValue")), n = r(toRef(a, "toggle")), u = r(toRef(a, "visible")), f = r(toRef(a, "isNav")), c = ref(), v = ref(), p = computed(() => ({
      show: l.value,
      "navbar-collapse": f.value
    })), B = () => t("update:modelValue", false);
    return watch(
      () => l.value,
      (m) => {
        var $, V;
        m ? ($ = v.value) == null || $.show() : (V = v.value) == null || V.hide();
      }
    ), watch(
      () => u.value,
      (m) => {
        var $, V;
        m ? (t("update:modelValue", !!m), ($ = v.value) == null || $.show()) : (t("update:modelValue", !!m), (V = v.value) == null || V.hide());
      }
    ), Be(c, "show.bs.collapse", () => {
      t("show"), t("update:modelValue", true);
    }), Be(c, "hide.bs.collapse", () => {
      t("hide"), t("update:modelValue", false);
    }), Be(c, "shown.bs.collapse", () => t("shown")), Be(c, "hidden.bs.collapse", () => t("hidden")), onMounted(() => {
      var m;
      v.value = new Collapse(c.value, {
        parent: a.accordion ? `#${a.accordion}` : void 0,
        toggle: n.value
      }), (u.value || l.value) && (t("update:modelValue", true), (m = v.value) == null || m.show());
    }), (m, $) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: e.id,
      ref_key: "element",
      ref: c,
      class: normalizeClass(["collapse", unref(p)]),
      "data-bs-parent": e.accordion || null,
      "is-nav": unref(f)
    }, {
      default: withCtx(() => [
        renderSlot(m.$slots, "default", {
          visible: unref(l),
          close: B
        })
      ]),
      _: 3
    }, 8, ["id", "class", "data-bs-parent", "is-nav"]));
  }
});
var En = {
  mounted(e, t) {
    let a = t.value;
    Object.keys(t.modifiers).length > 0 && ([a] = Object.keys(t.modifiers)), e.setAttribute("data-bs-toggle", "modal"), e.setAttribute("data-bs-target", `#${a}`);
  }
};
var Dn = {
  mounted(e, t) {
    let a = "right";
    const l = [];
    t.modifiers.left ? a = "left" : t.modifiers.right ? a = "right" : t.modifiers.bottom ? a = "bottom" : t.modifiers.top && (a = "top"), t.modifiers.manual ? l.push("manual") : (t.modifiers.click && l.push("click"), t.modifiers.hover && l.push("hover"), t.modifiers.focus && l.push("focus")), e.setAttribute("data-bs-toggle", "popover"), new Popover(e, {
      trigger: l.length === 0 ? "click" : l.join(" "),
      placement: a,
      content: t.value,
      html: t.modifiers.html
    });
  },
  unmounted(e) {
    const t = Popover.getInstance(e);
    t == null || t.dispose();
  }
};
var Hn = (e) => {
  if (e.classList.contains("offcanvas"))
    return "offcanvas";
  if (e.classList.contains("collapse"))
    return "collapse";
  throw Error("Couldn't resolve toggle type");
};
var Rn = (e, t) => {
  const { modifiers: a, arg: l, value: n } = e, u = Object.keys(a || {}), f = typeof n == "string" ? n.split(dt) : n;
  if (In(t.tagName, "a")) {
    const c = Kt(t, "href") || "";
    fn.test(c) && u.push(c.replace(cn, ""));
  }
  return Array.prototype.concat.apply([], [l, f]).forEach((c) => typeof c == "string" && u.push(c)), u.filter((c, v, p) => c && p.indexOf(c) === v);
};
var Yt = {
  mounted(e, t) {
    const a = Rn(t, e), l = [];
    let n = "data-bs-target";
    e.tagName === "a" && (n = "href");
    for (let u = 0; u < a.length; u++) {
      const f = a[u], c = document.getElementById(f);
      c && (e.setAttribute("data-bs-toggle", Hn(c)), l.push(`#${f}`));
    }
    l.length > 0 && e.setAttribute(n, l.join(","));
  }
};
var qn = (e, t) => {
  if (t != null && t.trigger)
    return t.trigger;
  if (e.manual)
    return "manual";
  const a = [];
  return e.click && a.push("click"), e.hover && a.push("hover"), e.focus && a.push("focus"), a.length > 0 ? a.join(" ") : "hover focus";
};
var Mn = (e, t) => t != null && t.placement ? t.placement : e.left ? "left" : e.right ? "right" : e.bottom ? "bottom" : "top";
var jn = (e) => e != null && e.delay ? e.delay : 0;
var sa = (e) => typeof e > "u" ? (console.warn(
  "Review tooltip directive usage. Some uses are not defining a title in root component or a value like `v-b-tooltip='{title: \"my title\"}'` nor `v-b-tooltip=\"'my title'\"` to define a title"
), "") : typeof e == "object" ? e == null ? void 0 : e.title : e;
var Gn = {
  beforeMount(e, t) {
    e.setAttribute("data-bs-toggle", "tooltip"), e.getAttribute("title") || e.setAttribute("title", sa(t.value).toString());
    const a = /<("[^"]*"|'[^']*'|[^'">])*>/.test(e.title), l = qn(t.modifiers, t.value), n = Mn(t.modifiers, t.value), u = jn(t.value), f = e.getAttribute("title");
    new Tooltip(e, {
      trigger: l,
      placement: n,
      delay: u,
      html: a
    }), f && e.setAttribute("data-bs-original-title", f);
  },
  updated(e, t) {
    e.getAttribute("title") || e.setAttribute("title", sa(t.value).toString());
    const a = e.getAttribute("title"), l = e.getAttribute("data-bs-original-title"), n = Tooltip.getInstance(e);
    e.removeAttribute("title"), a && a !== l && (n == null || n.setContent({ ".tooltip-inner": a }), e.setAttribute("data-bs-original-title", a));
  },
  unmounted(e) {
    const t = Tooltip.getInstance(e);
    t == null || t.dispose();
  }
};
var ct = /* @__PURE__ */ new Map();
var nl = (e) => {
  if (ct.has(e)) {
    const t = ct.get(e);
    t && t.stop && t.stop(), ct.delete(e);
  }
};
var ia = (e, t) => {
  const a = {
    margin: "0px",
    once: false,
    callback: t.value
  };
  Object.keys(t.modifiers).forEach((n) => {
    Number.isInteger(n) ? a.margin = `${n}px` : n.toLowerCase() === "once" && (a.once = true);
  }), nl(e);
  const l = new Wn(
    e,
    a.margin,
    a.once,
    a.callback,
    t.instance
  );
  ct.set(e, l);
};
var Un = {
  beforeMount(e, t) {
    ia(e, t);
  },
  updated(e, t) {
    ia(e, t);
  },
  unmounted(e) {
    nl(e);
  }
};
var Wn = class {
  constructor(t, a, l, n, u) {
    be(this, "element");
    be(this, "margin");
    be(this, "once");
    be(this, "callback");
    be(this, "instance");
    be(this, "observer");
    be(this, "doneOnce");
    be(this, "visible");
    this.element = t, this.margin = a, this.once = l, this.callback = n, this.instance = u, this.createObserver();
  }
  createObserver() {
    if (this.observer && this.stop(), !(this.doneOnce || typeof this.callback != "function")) {
      try {
        this.observer = new IntersectionObserver(this.handler.bind(this), {
          root: null,
          rootMargin: this.margin,
          threshold: 0
        });
      } catch {
        console.error("Intersection Observer not supported"), this.doneOnce = true, this.observer = void 0, this.callback(null);
        return;
      }
      this.instance.$nextTick(() => {
        this.observer && this.observer.observe(this.element);
      });
    }
  }
  handler(t) {
    const [a] = t, l = Boolean(a.isIntersecting || a.intersectionRatio > 0);
    l !== this.visible && (this.visible = l, this.callback(l), this.once && this.visible && (this.doneOnce = true, this.stop()));
  }
  stop() {
    this.observer && this.observer.disconnect(), this.observer = null;
  }
};
var Kn = {
  mounted(e, t) {
    t.value !== false && e.focus();
  }
};
var Xn = {
  BModal: En,
  BPopover: Dn,
  BToggle: Yt,
  BTooltip: Gn,
  BVisible: Un,
  focus: Kn
};
var Jn = { class: "accordion-item" };
var Yn = ["id"];
var Zn = ["aria-expanded", "aria-controls"];
var Qn = { class: "accordion-body" };
var eo = defineComponent({
  __name: "BAccordionItem",
  props: {
    id: null,
    title: null,
    visible: { default: false }
  },
  setup(e) {
    const t = e, a = inject(al, ""), l = ke(toRef(t, "id"), "accordion_item"), n = r(toRef(t, "visible"));
    return (u, f) => (openBlock(), createElementBlock("div", Jn, [
      createBaseVNode("h2", {
        id: `${unref(l)}heading`,
        class: "accordion-header"
      }, [
        withDirectives((openBlock(), createElementBlock("button", {
          class: normalizeClass(["accordion-button", { collapsed: !unref(n) }]),
          type: "button",
          "aria-expanded": unref(n) ? "true" : "false",
          "aria-controls": unref(l)
        }, [
          renderSlot(u.$slots, "title", {}, () => [
            createTextVNode(toDisplayString(e.title), 1)
          ])
        ], 10, Zn)), [
          [unref(Yt), void 0, unref(l)]
        ])
      ], 8, Yn),
      createVNode(ll, {
        id: unref(l),
        class: "accordion-collapse",
        visible: e.visible,
        accordion: unref(a),
        "aria-labelledby": `heading${unref(l)}`
      }, {
        default: withCtx(() => [
          createBaseVNode("div", Qn, [
            renderSlot(u.$slots, "default")
          ])
        ]),
        _: 3
      }, 8, ["id", "visible", "accordion", "aria-labelledby"])
    ]));
  }
});
var to = ["type", "disabled", "aria-label"];
var Ze = defineComponent({
  __name: "BCloseButton",
  props: {
    ariaLabel: { default: "Close" },
    disabled: { default: false },
    white: { default: false },
    type: { default: "button" }
  },
  emits: ["click"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "disabled")), n = r(toRef(a, "white")), u = computed(() => ({
      "btn-close-white": n.value
    }));
    return (f, c) => (openBlock(), createElementBlock("button", {
      type: e.type,
      class: normalizeClass(["btn-close", unref(u)]),
      disabled: unref(l),
      "aria-label": e.ariaLabel,
      onClick: c[0] || (c[0] = (v) => t("click", v))
    }, null, 10, to));
  }
});
var ao = defineComponent({
  __name: "BAlert",
  props: {
    dismissLabel: { default: "Close" },
    dismissible: { default: false },
    fade: { default: false },
    modelValue: { type: [Boolean, Number], default: false },
    show: { default: false },
    variant: { default: "info" }
  },
  emits: ["closed", "close-count-down", "update:modelValue"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "dismissible"));
    r(toRef(a, "fade"));
    const n = r(toRef(a, "show")), u = useSlots();
    let f;
    const c = ref(null), v = ref(), p = computed(() => !Ve(u.close)), B = computed(() => !!a.modelValue || n.value), m = computed(() => [
      [`alert-${a.variant}`],
      {
        show: !!a.modelValue,
        "alert-dismissible": l.value,
        fade: !!a.modelValue
      }
    ]), $ = (C) => {
      if (typeof C == "boolean")
        return 0;
      const q = Le(C, 0);
      return q > 0 ? q : 0;
    }, V = ref(0), T = computed(() => a.modelValue === true ? true : a.modelValue === false || Le(a.modelValue, 0) < 1 ? false : !!a.modelValue), b = () => {
      f !== void 0 && (clearTimeout(f), f = void 0);
    }, k = () => {
      V.value = $(a.modelValue), (T.value || n.value) && !v.value && (v.value = new Alert(c.value));
    }, y = () => {
      typeof a.modelValue == "boolean" ? t("update:modelValue", false) : t("update:modelValue", 0), t("closed");
    };
    return watch(() => a.modelValue, k), watch(() => n.value, k), watch(V, (C) => {
      b(), typeof a.modelValue != "boolean" && (t("close-count-down", C), C === 0 && a.modelValue > 0 && t("closed"), a.modelValue !== C && t("update:modelValue", C), C > 0 && (f = setTimeout(() => {
        V.value--;
      }, 1e3)));
    }), V.value = $(a.modelValue), onBeforeUnmount(() => {
      var C;
      b(), (C = v.value) == null || C.dispose(), v.value = void 0;
    }), (C, q) => unref(B) ? (openBlock(), createElementBlock("div", {
      key: 0,
      ref_key: "element",
      ref: c,
      class: normalizeClass(["alert", unref(m)]),
      role: "alert"
    }, [
      renderSlot(C.$slots, "default"),
      unref(l) ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        unref(p) ? (openBlock(), createElementBlock("button", {
          key: 0,
          type: "button",
          "data-bs-dismiss": "alert",
          onClick: y
        }, [
          renderSlot(C.$slots, "close")
        ])) : (openBlock(), createBlock(Ze, {
          key: 1,
          "aria-label": e.dismissLabel,
          "data-bs-dismiss": "alert",
          onClick: y
        }, null, 8, ["aria-label"]))
      ], 64)) : createCommentVNode("", true)
    ], 2)) : createCommentVNode("", true);
  }
});
var ol = Symbol();
var lo = defineComponent({
  __name: "BAvatarGroup",
  props: {
    overlap: { default: 0.3 },
    rounded: { type: [Boolean, String], default: false },
    size: null,
    square: { default: false },
    tag: { default: "div" },
    variant: null
  },
  setup(e) {
    const t = e, a = r(toRef(t, "square")), l = computed(() => zt(t.size)), n = computed(
      () => Math.min(Math.max(f(t.overlap), 0), 1) / 2
    ), u = computed(() => {
      const c = l.value ? `calc(${l.value} * ${n.value})` : null;
      return c ? { paddingLeft: c, paddingRight: c } : {};
    }), f = (c) => typeof c == "string" && Pa(c) ? tt(c, 0) : c || 0;
    return provide(ol, {
      overlapScale: n,
      size: t.size,
      square: a.value,
      rounded: t.rounded,
      variant: t.variant
    }), (c, v) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: "b-avatar-group",
      role: "group"
    }, {
      default: withCtx(() => [
        createBaseVNode("div", {
          class: "b-avatar-group-inner",
          style: normalizeStyle(unref(u))
        }, [
          renderSlot(c.$slots, "default")
        ], 4)
      ]),
      _: 3
    }));
  }
});
var no = {
  key: 0,
  class: "b-avatar-custom"
};
var oo = {
  key: 1,
  class: "b-avatar-img"
};
var so = ["src", "alt"];
var zt = (e) => {
  const t = typeof e == "string" && Pa(e) ? tt(e, 0) : e;
  return typeof t == "number" ? `${t}px` : t || null;
};
var io = defineComponent({
  __name: "BAvatar",
  props: {
    alt: { default: "avatar" },
    ariaLabel: null,
    badge: { type: [Boolean, String], default: false },
    badgeLeft: { default: false },
    badgeOffset: null,
    badgeTop: { default: false },
    badgeVariant: { default: "primary" },
    button: { default: false },
    buttonType: { default: "button" },
    disabled: { default: false },
    icon: null,
    rounded: { type: [Boolean, String], default: "circle" },
    size: null,
    square: { default: false },
    src: null,
    text: null,
    textVariant: null,
    variant: { default: "secondary" }
  },
  emits: ["click", "img-error"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = inject(ol, null), u = ["sm", null, "lg"], f = 0.4, c = f * 0.7, v = r(toRef(a, "badgeLeft")), p = r(toRef(a, "badgeTop")), B = r(toRef(a, "button")), m = r(toRef(a, "disabled")), $ = r(toRef(a, "square")), V = computed(() => !Ve(l.default)), T = computed(() => !Ve(l.badge)), b = computed(() => !!a.badge || a.badge === "" || T.value), k = computed(
      () => n != null && n.size ? n.size : zt(a.size)
    ), y = computed(
      () => n != null && n.variant ? n.variant : a.variant
    ), C = computed(
      () => n != null && n.rounded ? n.rounded : a.rounded
    ), q = computed(() => ({
      type: B.value ? a.buttonType : void 0,
      "aria-label": a.ariaLabel || null,
      disabled: m.value || null
    })), E = computed(() => [`bg-${a.badgeVariant}`]), I = computed(() => a.badge === true ? "" : a.badge), _ = computed(() => [[`text-${ie(a.badgeVariant)}`]]), z = computed(() => ({
      [`b-avatar-${a.size}`]: !!a.size && u.indexOf(zt(a.size)) !== -1,
      [`bg-${y.value}`]: !!y.value,
      badge: !B.value && y.value && V.value,
      rounded: C.value === "" || C.value === true,
      ["rounded-circle"]: !$.value && C.value === "circle",
      ["rounded-0"]: $.value || C.value === "0",
      ["rounded-1"]: !$.value && C.value === "sm",
      ["rounded-3"]: !$.value && C.value === "lg",
      ["rounded-top"]: !$.value && C.value === "top",
      ["rounded-bottom"]: !$.value && C.value === "bottom",
      ["rounded-start"]: !$.value && C.value === "left",
      ["rounded-end"]: !$.value && C.value === "right",
      btn: B.value,
      [`btn-${y.value}`]: B.value ? !!y.value : false
    })), x = computed(() => [
      [`text-${a.textVariant || ie(y.value)}`]
    ]), w = computed(() => {
      const ae = a.badgeOffset || "0px";
      return {
        fontSize: (u.indexOf(k.value || null) === -1 ? `calc(${k.value} * ${c})` : "") || "",
        top: p.value ? ae : "",
        bottom: p.value ? "" : ae,
        left: v.value ? ae : "",
        right: v.value ? "" : ae
      };
    }), P = computed(() => {
      const ae = u.indexOf(k.value || null) === -1 ? `calc(${k.value} * ${f})` : null;
      return ae ? { fontSize: ae } : {};
    }), L = computed(() => {
      var ye;
      const ae = ((ye = n == null ? void 0 : n.overlapScale) == null ? void 0 : ye.value) || 0, ge = k.value && ae ? `calc(${k.value} * -${ae})` : null;
      return ge ? { marginLeft: ge, marginRight: ge } : {};
    }), te = computed(() => B.value ? "button" : "span"), Q = computed(() => ({
      ...L.value,
      width: k.value,
      height: k.value
    })), ie = (ae) => ae === "light" || ae === "warning" ? "dark" : "light", K = (ae) => {
      !m.value && B.value && t("click", ae);
    }, me = (ae) => t("img-error", ae);
    return (ae, ge) => (openBlock(), createBlock(resolveDynamicComponent(unref(te)), mergeProps({
      class: ["b-avatar", unref(z)],
      style: unref(Q)
    }, unref(q), { onClick: K }), {
      default: withCtx(() => [
        unref(V) ? (openBlock(), createElementBlock("span", no, [
          renderSlot(ae.$slots, "default")
        ])) : e.src ? (openBlock(), createElementBlock("span", oo, [
          createBaseVNode("img", {
            src: e.src,
            alt: e.alt,
            onError: me
          }, null, 40, so)
        ])) : e.text ? (openBlock(), createElementBlock("span", {
          key: 2,
          class: normalizeClass(["b-avatar-text", unref(x)]),
          style: normalizeStyle(unref(P))
        }, toDisplayString(e.text), 7)) : createCommentVNode("", true),
        unref(b) ? (openBlock(), createElementBlock("span", {
          key: 3,
          class: normalizeClass(["b-avatar-badge", unref(E)]),
          style: normalizeStyle(unref(w))
        }, [
          unref(T) ? renderSlot(ae.$slots, "badge", { key: 0 }) : (openBlock(), createElementBlock("span", {
            key: 1,
            class: normalizeClass(unref(_))
          }, toDisplayString(unref(I)), 3))
        ], 6)) : createCommentVNode("", true)
      ]),
      _: 3
    }, 16, ["class", "style"]));
  }
});
var Ge = {
  active: { type: [Boolean, String], default: false },
  activeClass: { type: String, default: "router-link-active" },
  append: { type: [Boolean, String], default: false },
  disabled: { type: [Boolean, String], default: false },
  event: { type: [String, Array], default: "click" },
  exact: { type: [Boolean, String], default: false },
  exactActiveClass: { type: String, default: "router-link-exact-active" },
  href: { type: String },
  rel: { type: String, default: null },
  replace: { type: [Boolean, String], default: false },
  routerComponentName: { type: String, default: "router-link" },
  routerTag: { type: String, default: "a" },
  target: { type: String, default: "_self" },
  to: { type: [String, Object], default: null }
};
var ro = defineComponent({
  props: Ge,
  emits: ["click"],
  setup(e, { emit: t, attrs: a }) {
    const l = r(toRef(e, "active")), n = r(toRef(e, "append")), u = r(toRef(e, "disabled")), f = r(toRef(e, "exact")), c = r(toRef(e, "replace")), v = getCurrentInstance(), p = ref(null), B = computed(() => {
      const b = e.routerComponentName.split("-").map((y) => y.charAt(0).toUpperCase() + y.slice(1)).join("");
      return !((v == null ? void 0 : v.appContext.app.component(b)) !== void 0) || u.value || !e.to ? "a" : e.routerComponentName;
    }), m = computed(() => {
      const b = "#";
      if (e.href)
        return e.href;
      if (typeof e.to == "string")
        return e.to || b;
      const k = e.to;
      if (Object.prototype.toString.call(k) === "[object Object]" && (k.path || k.query || k.hash)) {
        const y = k.path || "", C = k.query ? `?${Object.keys(k.query).map((E) => `${E}=${k.query[E]}`).join("=")}` : "", q = !k.hash || k.hash.charAt(0) === "#" ? k.hash || "" : `#${k.hash}`;
        return `${y}${C}${q}` || b;
      }
      return b;
    }), $ = computed(() => ({
      to: e.to,
      href: m.value,
      target: e.target,
      rel: e.target === "_blank" && e.rel === null ? "noopener" : e.rel || null,
      tabindex: u.value ? "-1" : typeof a.tabindex > "u" ? null : a.tabindex,
      "aria-disabled": u.value ? "true" : null
    }));
    return {
      computedLinkClasses: computed(() => ({
        active: l.value,
        disabled: u.value
      })),
      tag: B,
      routerAttr: $,
      link: p,
      clicked: (b) => {
        if (u.value) {
          b.preventDefault(), b.stopImmediatePropagation();
          return;
        }
        t("click", b);
      },
      activeBoolean: l,
      appendBoolean: n,
      disabledBoolean: u,
      replaceBoolean: c,
      exactBoolean: f
    };
  }
});
var _e = (e, t) => {
  const a = e.__vccOpts || e;
  for (const [l, n] of t)
    a[l] = n;
  return a;
};
function uo(e, t, a, l, n, u) {
  return e.tag === "router-link" ? (openBlock(), createBlock(resolveDynamicComponent(e.tag), mergeProps({ key: 0 }, e.routerAttr, { custom: "" }), {
    default: withCtx(({ href: f, navigate: c, isActive: v, isExactActive: p }) => [
      (openBlock(), createBlock(resolveDynamicComponent(e.routerTag), mergeProps({
        ref: "link",
        href: f,
        class: [
          (v || e.activeBoolean) && e.activeClass,
          (p || e.exactBoolean) && e.exactActiveClass
        ]
      }, e.$attrs, { onClick: c }), {
        default: withCtx(() => [
          renderSlot(e.$slots, "default")
        ]),
        _: 2
      }, 1040, ["href", "class", "onClick"]))
    ]),
    _: 3
  }, 16)) : (openBlock(), createBlock(resolveDynamicComponent(e.tag), mergeProps({
    key: 1,
    ref: "link",
    class: e.computedLinkClasses
  }, e.routerAttr, { onClick: e.clicked }), {
    default: withCtx(() => [
      renderSlot(e.$slots, "default")
    ]),
    _: 3
  }, 16, ["class", "onClick"]));
}
var Ae = _e(ro, [["render", uo]]);
var ra = kt(Ge, ["event", "routerTag"]);
var co = defineComponent({
  components: { BLink: Ae },
  props: {
    pill: { type: [Boolean, String], default: false },
    tag: { type: String, default: "span" },
    variant: { type: String, default: "secondary" },
    textIndicator: { type: [Boolean, String], default: false },
    dotIndicator: { type: [Boolean, String], default: false },
    ...ra
  },
  setup(e) {
    const t = r(toRef(e, "pill")), a = r(toRef(e, "textIndicator")), l = r(toRef(e, "dotIndicator")), n = r(toRef(e, "active")), u = r(toRef(e, "disabled")), f = computed(() => at(e)), c = computed(
      () => f.value ? Ae : e.tag
    ), v = computed(() => [
      [`bg-${e.variant}`],
      {
        active: n.value,
        disabled: u.value,
        "text-dark": ["warning", "info", "light"].includes(e.variant),
        "rounded-pill": t.value,
        "position-absolute top-0 start-100 translate-middle": a.value || l.value,
        "p-2 border border-light rounded-circle": l.value,
        "text-decoration-none": f.value
      }
    ]), p = computed(
      () => f.value ? Xt(e, ra) : {}
    );
    return {
      computedClasses: v,
      computedLinkProps: p,
      computedTag: c
    };
  }
});
function fo(e, t, a, l, n, u) {
  return openBlock(), createBlock(resolveDynamicComponent(e.computedTag), mergeProps({
    class: ["badge", e.computedClasses]
  }, e.computedLinkProps), {
    default: withCtx(() => [
      renderSlot(e.$slots, "default")
    ]),
    _: 3
  }, 16, ["class"]);
}
var vo = _e(co, [["render", fo]]);
var ua = kt(Ge, ["event", "routerTag"]);
var mo = defineComponent({
  components: { BLink: Ae },
  props: {
    ...ua,
    active: { type: [Boolean, String], default: false },
    ariaCurrent: { type: String, default: "location" },
    disabled: { type: [Boolean, String], default: false },
    text: { type: String, required: false }
  },
  emits: ["click"],
  setup(e, { emit: t }) {
    const a = r(toRef(e, "active")), l = r(toRef(e, "disabled")), n = computed(() => ({
      active: a.value
    })), u = computed(
      () => a.value ? "span" : Ae
    ), f = computed(
      () => a.value ? e.ariaCurrent : void 0
    );
    return {
      computedLinkProps: computed(
        () => u.value !== "span" ? Xt(e, ua) : {}
      ),
      computedClasses: n,
      computedTag: u,
      computedAriaCurrent: f,
      clicked: (p) => {
        if (l.value || a.value) {
          p.preventDefault(), p.stopImmediatePropagation();
          return;
        }
        l.value || t("click", p);
      }
    };
  }
});
function bo(e, t, a, l, n, u) {
  return openBlock(), createElementBlock("li", {
    class: normalizeClass(["breadcrumb-item", e.computedClasses])
  }, [
    (openBlock(), createBlock(resolveDynamicComponent(e.computedTag), mergeProps({ "aria-current": e.computedAriaCurrent }, e.computedLinkProps, { onClick: e.clicked }), {
      default: withCtx(() => [
        renderSlot(e.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 16, ["aria-current", "onClick"]))
  ], 2);
}
var sl = _e(mo, [["render", bo]]);
var go = { "aria-label": "breadcrumb" };
var po = { class: "breadcrumb" };
var ho = defineComponent({
  __name: "BBreadcrumb",
  props: {
    items: null
  },
  setup(e) {
    const t = e, a = Ln(), l = computed(() => {
      const n = t.items || (a == null ? void 0 : a.items) || [];
      let u = false;
      return n.map((c, v) => (typeof c == "string" && (c = { text: c }, v < n.length - 1 && (c.href = "#")), c.active && (u = true), !c.active && !u && (c.active = v + 1 === n.length), c));
    });
    return (n, u) => (openBlock(), createElementBlock("nav", go, [
      createBaseVNode("ol", po, [
        renderSlot(n.$slots, "prepend"),
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l), (f, c) => (openBlock(), createBlock(sl, mergeProps({ key: c }, f), {
          default: withCtx(() => [
            createTextVNode(toDisplayString(f.text), 1)
          ]),
          _: 2
        }, 1040))), 128)),
        renderSlot(n.$slots, "default"),
        renderSlot(n.$slots, "append")
      ])
    ]));
  }
});
var yo = {
  key: 0,
  class: "visually-hidden"
};
var Ct = defineComponent({
  __name: "BSpinner",
  props: {
    label: null,
    role: { default: "status" },
    small: { default: false },
    tag: { default: "span" },
    type: { default: "border" },
    variant: null
  },
  setup(e) {
    const t = e, a = useSlots(), l = r(toRef(t, "small")), n = computed(() => ({
      "spinner-border": t.type === "border",
      "spinner-border-sm": t.type === "border" && l.value,
      "spinner-grow": t.type === "grow",
      "spinner-grow-sm": t.type === "grow" && l.value,
      [`text-${t.variant}`]: t.variant !== void 0
    })), u = computed(() => !Ve(a.label));
    return (f, c) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(unref(n)),
      role: e.label || unref(u) ? e.role : null,
      "aria-hidden": e.label || unref(u) ? null : true
    }, {
      default: withCtx(() => [
        e.label || unref(u) ? (openBlock(), createElementBlock("span", yo, [
          renderSlot(f.$slots, "label", {}, () => [
            createTextVNode(toDisplayString(e.label), 1)
          ])
        ])) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["class", "role", "aria-hidden"]));
  }
});
var Bo = defineComponent({
  components: { BLink: Ae, BSpinner: Ct },
  props: {
    ...Ge,
    active: { type: [Boolean, String], default: false },
    disabled: { type: [Boolean, String], default: false },
    href: { type: String, required: false },
    pill: { type: [Boolean, String], default: false },
    pressed: { type: [Boolean, String], default: false },
    rel: { type: String, default: void 0 },
    size: { type: String, default: "md" },
    squared: { type: [Boolean, String], default: false },
    tag: { type: String, default: "button" },
    target: { type: String, default: "_self" },
    type: { type: String, default: "button" },
    variant: { type: String, default: "secondary" },
    loading: { type: [Boolean, String], default: false },
    loadingMode: { type: String, default: "inline" }
  },
  emits: ["click", "update:pressed"],
  setup(e, { emit: t }) {
    const a = r(toRef(e, "active")), l = r(toRef(e, "disabled")), n = r(toRef(e, "pill")), u = r(toRef(e, "pressed")), f = r(toRef(e, "squared")), c = r(toRef(e, "loading")), v = computed(() => u.value === true), p = computed(
      () => e.tag === "button" && e.href === void 0 && e.to === null
    ), B = computed(() => at(e)), m = computed(() => e.to !== null), $ = computed(
      () => e.href !== void 0 ? false : !p.value
    ), V = computed(() => [
      [`btn-${e.variant}`],
      [`btn-${e.size}`],
      {
        active: a.value || u.value,
        "rounded-pill": n.value,
        "rounded-0": f.value,
        disabled: l.value
      }
    ]), T = computed(() => ({
      "aria-disabled": $.value ? l.value : null,
      "aria-pressed": v.value ? u.value : null,
      autocomplete: v.value ? "off" : null,
      disabled: p.value ? l.value : null,
      href: e.href,
      rel: B.value ? e.rel : null,
      role: $.value || B.value ? "button" : null,
      target: B.value ? e.target : null,
      type: p.value ? e.type : null,
      to: p.value ? null : e.to,
      append: B.value ? e.append : null,
      activeClass: m.value ? e.activeClass : null,
      event: m.value ? e.event : null,
      exact: m.value ? e.exact : null,
      exactActiveClass: m.value ? e.exactActiveClass : null,
      replace: m.value ? e.replace : null,
      routerComponentName: m.value ? e.routerComponentName : null,
      routerTag: m.value ? e.routerTag : null
    })), b = computed(
      () => m.value ? Ae : e.href ? "a" : e.tag
    );
    return {
      computedClasses: V,
      computedAttrs: T,
      computedTag: b,
      clicked: (y) => {
        if (l.value) {
          y.preventDefault(), y.stopPropagation();
          return;
        }
        t("click", y), v.value && t("update:pressed", !u.value);
      },
      loadingBoolean: c
    };
  }
});
function $o(e, t, a, l, n, u) {
  const f = resolveComponent("b-spinner");
  return openBlock(), createBlock(resolveDynamicComponent(e.computedTag), mergeProps({
    class: ["btn", e.computedClasses]
  }, e.computedAttrs, { onClick: e.clicked }), {
    default: withCtx(() => [
      e.loadingBoolean ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: normalizeClass(["btn-loading", { "mode-fill": e.loadingMode === "fill", "mode-inline": e.loadingMode === "inline" }])
      }, [
        renderSlot(e.$slots, "loading", {}, () => [
          createVNode(f, {
            class: "btn-spinner",
            small: e.size !== "lg"
          }, null, 8, ["small"])
        ])
      ], 2)) : createCommentVNode("", true),
      createBaseVNode("div", {
        class: normalizeClass(["btn-content", { "btn-loading-fill": e.loadingBoolean && e.loadingMode === "fill" }])
      }, [
        renderSlot(e.$slots, "default")
      ], 2)
    ]),
    _: 3
  }, 16, ["class", "onClick"]);
}
var lt = _e(Bo, [["render", $o]]);
var ko = defineComponent({
  __name: "BButtonGroup",
  props: {
    ariaLabel: { default: "Group" },
    size: null,
    tag: { default: "div" },
    vertical: { default: false }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "vertical")), l = computed(() => ({
      "btn-group": !a.value,
      [`btn-group-${t.size}`]: t.size !== void 0,
      "btn-group-vertical": a.value
    }));
    return (n, u) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(unref(l)),
      role: "group",
      "aria-label": e.ariaLabel
    }, {
      default: withCtx(() => [
        renderSlot(n.$slots, "default")
      ]),
      _: 3
    }, 8, ["class", "aria-label"]));
  }
});
var Co = ["role", "aria-label"];
var So = defineComponent({
  __name: "BButtonToolbar",
  props: {
    ariaLabel: { default: "Group" },
    justify: { default: false },
    role: { default: "toolbar" }
  },
  setup(e) {
    const a = r(toRef(e, "justify")), l = computed(() => ({
      "justify-content-between": a.value
    }));
    return (n, u) => (openBlock(), createElementBlock("div", {
      class: normalizeClass([unref(l), "btn-toolbar"]),
      role: e.role,
      "aria-label": e.ariaLabel
    }, [
      renderSlot(n.$slots, "default")
    ], 10, Co));
  }
});
var Zt = defineComponent({
  __name: "BImg",
  props: {
    alt: null,
    blank: { default: false },
    blankColor: { default: "transparent" },
    block: { default: false },
    center: { default: false },
    fluid: { default: false },
    lazy: { default: false },
    fluidGrow: { default: false },
    height: null,
    left: { default: false },
    start: { default: false },
    right: { default: false },
    end: { default: false },
    rounded: { type: [Boolean, String], default: false },
    sizes: null,
    src: null,
    srcset: null,
    thumbnail: { default: false },
    width: null
  },
  emits: ["load"],
  setup(e, { emit: t }) {
    const a = e, l = '<svg width="%{w}" height="%{h}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 %{w} %{h}" preserveAspectRatio="none"><rect width="100%" height="100%" style="fill:%{f};"></rect></svg>', n = r(toRef(a, "lazy")), u = r(toRef(a, "blank")), f = r(toRef(a, "block")), c = r(toRef(a, "center")), v = r(toRef(a, "fluid")), p = r(toRef(a, "fluidGrow")), B = r(toRef(a, "left")), m = r(toRef(a, "start")), $ = r(toRef(a, "right")), V = r(toRef(a, "end")), T = r(toRef(a, "thumbnail")), b = computed(
      () => typeof a.srcset == "string" ? a.srcset.split(",").filter((z) => z).join(",") : Array.isArray(a.srcset) ? a.srcset.filter((z) => z).join(",") : void 0
    ), k = computed(
      () => typeof a.sizes == "string" ? a.sizes.split(",").filter((z) => z).join(",") : Array.isArray(a.sizes) ? a.sizes.filter((z) => z).join(",") : void 0
    ), y = computed(() => {
      const z = (P) => P === void 0 ? void 0 : typeof P == "number" ? P : Number.parseInt(P, 10) || void 0, x = z(a.width), w = z(a.height);
      if (u.value) {
        if (x !== void 0 && w === void 0)
          return { height: x, width: x };
        if (x === void 0 && w !== void 0)
          return { height: w, width: w };
        if (x === void 0 && w === void 0)
          return { height: 1, width: 1 };
      }
      return {
        width: x,
        height: w
      };
    }), C = computed(
      () => _(y.value.width, y.value.height, a.blankColor)
    ), q = computed(() => ({
      src: u.value ? C.value : a.src,
      alt: a.alt,
      width: y.value.width || void 0,
      height: y.value.height || void 0,
      srcset: u.value ? void 0 : b.value,
      sizes: u.value ? void 0 : k.value,
      loading: n.value ? "lazy" : "eager"
    })), E = computed(
      () => B.value || m.value ? "float-start" : $.value || V.value ? "float-end" : c.value ? "mx-auto" : void 0
    ), I = computed(() => ({
      "img-thumbnail": T.value,
      "img-fluid": v.value || p.value,
      "w-100": p.value,
      rounded: a.rounded === "" || a.rounded === true,
      [`rounded-${a.rounded}`]: typeof a.rounded == "string" && a.rounded !== "",
      [`${E.value}`]: E.value !== void 0,
      "d-block": f.value || c.value
    })), _ = (z, x, w) => `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(
      l.replace("%{w}", String(z)).replace("%{h}", String(x)).replace("%{f}", w)
    )}`;
    return (z, x) => (openBlock(), createElementBlock("img", mergeProps({ class: unref(I) }, unref(q), {
      onLoad: x[0] || (x[0] = (w) => t("load", w))
    }), null, 16));
  }
});
var pt = defineComponent({
  __name: "BCardImg",
  props: {
    alt: null,
    blank: { default: false },
    blankColor: null,
    bottom: { default: false },
    lazy: { default: false },
    height: null,
    left: { default: false },
    start: { default: false },
    right: { default: false },
    end: { default: false },
    sizes: null,
    src: null,
    srcset: null,
    top: { default: false },
    width: null
  },
  emits: ["load"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "bottom")), n = r(toRef(a, "end")), u = r(toRef(a, "left")), f = r(toRef(a, "right")), c = r(toRef(a, "start")), v = r(toRef(a, "top")), p = computed(
      () => v.value ? "card-img-top" : f.value || n.value ? "card-img-right" : l.value ? "card-img-bottom" : u.value || c.value ? "card-img-left" : "card-img"
    ), B = computed(() => ({
      alt: a.alt,
      height: a.height,
      src: a.src,
      lazy: a.lazy,
      width: a.width,
      blank: a.blank,
      blankColor: a.blankColor,
      sizes: a.sizes,
      srcset: a.srcset
    }));
    return (m, $) => (openBlock(), createBlock(Zt, mergeProps({ class: unref(p) }, unref(B), {
      onLoad: $[0] || ($[0] = (V) => t("load", V))
    }), null, 16, ["class"]));
  }
});
var wo = ["innerHTML"];
var il = defineComponent({
  __name: "BCardHeadFoot",
  props: {
    text: null,
    bgVariant: null,
    borderVariant: null,
    html: null,
    tag: { default: "div" },
    textVariant: null
  },
  setup(e) {
    const t = e, a = computed(() => ({
      [`text-${t.textVariant}`]: t.textVariant !== void 0,
      [`bg-${t.bgVariant}`]: t.bgVariant !== void 0,
      [`border-${t.borderVariant}`]: t.borderVariant !== void 0
    }));
    return (l, n) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(unref(a))
    }, {
      default: withCtx(() => [
        e.html ? (openBlock(), createElementBlock("div", {
          key: 0,
          innerHTML: e.html
        }, null, 8, wo)) : renderSlot(l.$slots, "default", { key: 1 }, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var rl = defineComponent({
  __name: "BCardHeader",
  props: {
    text: null,
    bgVariant: null,
    borderVariant: null,
    html: null,
    tag: { default: "div" },
    textVariant: null
  },
  setup(e) {
    const t = e;
    return (a, l) => (openBlock(), createBlock(il, mergeProps({ class: "card-header" }, t), {
      default: withCtx(() => [
        renderSlot(a.$slots, "default")
      ]),
      _: 3
    }, 16));
  }
});
var ul = defineComponent({
  __name: "BCardTitle",
  props: {
    text: null,
    tag: { default: "h4" }
  },
  setup(e) {
    return (t, a) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), { class: "card-title" }, {
      default: withCtx(() => [
        renderSlot(t.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }));
  }
});
var dl = defineComponent({
  __name: "BCardSubtitle",
  props: {
    text: null,
    tag: { default: "h6" },
    textVariant: { default: "muted" }
  },
  setup(e) {
    const t = e, a = computed(() => [`text-${t.textVariant}`]);
    return (l, n) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(["card-subtitle mb-2", unref(a)])
    }, {
      default: withCtx(() => [
        renderSlot(l.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var cl = defineComponent({
  __name: "BCardBody",
  props: {
    bodyBgVariant: null,
    bodyTag: { default: "div" },
    bodyTextVariant: null,
    overlay: { default: false },
    subtitle: null,
    subtitleTag: { default: "h4" },
    subtitleTextVariant: null,
    title: null,
    titleTag: { default: "h4" },
    text: null
  },
  setup(e) {
    const t = e, a = useSlots(), l = r(toRef(t, "overlay")), n = computed(() => !Ve(a.title)), u = computed(() => !Ve(a.subtitle)), f = computed(() => ({
      "card-img-overlay": l.value,
      [`text-${t.bodyTextVariant}`]: t.bodyTextVariant !== void 0,
      [`bg-${t.bodyBgVariant}`]: t.bodyBgVariant !== void 0
    }));
    return (c, v) => (openBlock(), createBlock(resolveDynamicComponent(e.bodyTag), {
      class: normalizeClass(["card-body", unref(f)])
    }, {
      default: withCtx(() => [
        !!e.title || unref(n) ? (openBlock(), createBlock(ul, {
          key: 0,
          tag: e.titleTag
        }, {
          default: withCtx(() => [
            renderSlot(c.$slots, "title", {}, () => [
              createTextVNode(toDisplayString(e.title), 1)
            ])
          ]),
          _: 3
        }, 8, ["tag"])) : createCommentVNode("", true),
        !!e.subtitle || unref(u) ? (openBlock(), createBlock(dl, {
          key: 1,
          tag: e.subtitleTag,
          "text-variant": e.subtitleTextVariant
        }, {
          default: withCtx(() => [
            renderSlot(c.$slots, "subtitle", {}, () => [
              createTextVNode(toDisplayString(e.subtitle), 1)
            ])
          ]),
          _: 3
        }, 8, ["tag", "text-variant"])) : createCommentVNode("", true),
        renderSlot(c.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var fl = defineComponent({
  __name: "BCardFooter",
  props: {
    text: null,
    bgVariant: null,
    borderVariant: null,
    html: null,
    tag: { default: "div" },
    textVariant: null
  },
  setup(e) {
    const t = e;
    return (a, l) => (openBlock(), createBlock(il, mergeProps({ class: "card-footer" }, t), {
      default: withCtx(() => [
        renderSlot(a.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 16));
  }
});
var vl = defineComponent({
  __name: "BCard",
  props: {
    align: null,
    bgVariant: null,
    bodyBgVariant: null,
    bodyClass: null,
    bodyTag: { default: "div" },
    bodyTextVariant: null,
    borderVariant: null,
    footer: null,
    footerBgVariant: null,
    footerBorderVariant: null,
    footerClass: null,
    footerHtml: { default: "" },
    footerTag: { default: "div" },
    footerTextVariant: null,
    header: null,
    headerBgVariant: null,
    headerBorderVariant: null,
    headerClass: null,
    headerHtml: { default: "" },
    headerTag: { default: "div" },
    headerTextVariant: null,
    imgAlt: null,
    imgBottom: { default: false },
    imgEnd: { default: false },
    imgHeight: null,
    imgLeft: { default: false },
    imgRight: { default: false },
    imgSrc: null,
    imgStart: { default: false },
    imgTop: { default: false },
    imgWidth: null,
    noBody: { default: false },
    overlay: { default: false },
    subtitle: null,
    subtitleTag: { default: "h6" },
    subtitleTextVariant: { default: "muted" },
    tag: { default: "div" },
    textVariant: null,
    title: null,
    titleTag: { default: "h4" },
    bodyText: { default: "" }
  },
  setup(e) {
    const t = e, a = useSlots(), l = r(toRef(t, "imgBottom")), n = r(toRef(t, "imgEnd")), u = r(toRef(t, "imgLeft")), f = r(toRef(t, "imgRight")), c = r(toRef(t, "imgStart")), v = r(toRef(t, "noBody")), p = computed(() => !Ve(a.header)), B = computed(() => !Ve(a.footer)), m = computed(() => ({
      [`text-${t.align}`]: t.align !== void 0,
      [`text-${t.textVariant}`]: t.textVariant !== void 0,
      [`bg-${t.bgVariant}`]: t.bgVariant !== void 0,
      [`border-${t.borderVariant}`]: t.borderVariant !== void 0,
      "flex-row": u.value || c.value,
      "flex-row-reverse": n.value || f.value
    })), $ = computed(() => ({
      bgVariant: t.headerBgVariant,
      borderVariant: t.headerBorderVariant,
      html: t.headerHtml,
      tag: t.headerTag,
      textVariant: t.headerTextVariant
    })), V = computed(() => ({
      overlay: t.overlay,
      bodyBgVariant: t.bodyBgVariant,
      bodyTag: t.bodyTag,
      bodyTextVariant: t.bodyTextVariant,
      subtitle: t.subtitle,
      subtitleTag: t.subtitleTag,
      subtitleTextVariant: t.subtitleTextVariant,
      title: t.title,
      titleTag: t.titleTag
    })), T = computed(() => ({
      bgVariant: t.footerBgVariant,
      borderVariant: t.footerBorderVariant,
      html: t.footerHtml,
      tag: t.footerTag,
      textVariant: t.footerTextVariant
    })), b = computed(() => ({
      src: t.imgSrc,
      alt: t.imgAlt,
      height: t.imgHeight,
      width: t.imgWidth,
      bottom: t.imgBottom,
      end: t.imgEnd,
      left: t.imgLeft,
      right: t.imgRight,
      start: t.imgStart,
      top: t.imgTop
    }));
    return (k, y) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(["card", unref(m)])
    }, {
      default: withCtx(() => [
        unref(l) ? createCommentVNode("", true) : renderSlot(k.$slots, "img", { key: 0 }, () => [
          e.imgSrc ? (openBlock(), createBlock(pt, normalizeProps(mergeProps({ key: 0 }, unref(b))), null, 16)) : createCommentVNode("", true)
        ]),
        e.header || unref(p) || e.headerHtml ? (openBlock(), createBlock(rl, mergeProps({ key: 1 }, unref($), { class: e.headerClass }), {
          default: withCtx(() => [
            renderSlot(k.$slots, "header", {}, () => [
              createTextVNode(toDisplayString(e.header), 1)
            ])
          ]),
          _: 3
        }, 16, ["class"])) : createCommentVNode("", true),
        unref(v) ? renderSlot(k.$slots, "default", { key: 3 }, () => [
          createTextVNode(toDisplayString(e.bodyText), 1)
        ]) : (openBlock(), createBlock(cl, mergeProps({ key: 2 }, unref(V), { class: e.bodyClass }), {
          default: withCtx(() => [
            renderSlot(k.$slots, "default", {}, () => [
              createTextVNode(toDisplayString(e.bodyText), 1)
            ])
          ]),
          _: 3
        }, 16, ["class"])),
        e.footer || unref(B) || e.footerHtml ? (openBlock(), createBlock(fl, mergeProps({ key: 4 }, unref(T), { class: e.footerClass }), {
          default: withCtx(() => [
            renderSlot(k.$slots, "footer", {}, () => [
              createTextVNode(toDisplayString(e.footer), 1)
            ])
          ]),
          _: 3
        }, 16, ["class"])) : createCommentVNode("", true),
        unref(l) ? renderSlot(k.$slots, "img", { key: 5 }, () => [
          e.imgSrc ? (openBlock(), createBlock(pt, normalizeProps(mergeProps({ key: 0 }, unref(b))), null, 16)) : createCommentVNode("", true)
        ]) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var To = defineComponent({
  __name: "BCardGroup",
  props: {
    columns: { default: false },
    deck: { default: false },
    tag: { default: "div" }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "columns")), l = r(toRef(t, "deck")), n = computed(
      () => l.value ? "card-deck" : a.value ? "card-columns" : "card-group"
    ), u = computed(() => [n.value]);
    return (f, c) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(unref(u))
    }, {
      default: withCtx(() => [
        renderSlot(f.$slots, "default")
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var _o = defineComponent({
  __name: "BCardText",
  props: {
    text: null,
    tag: { default: "p" }
  },
  setup(e) {
    return (t, a) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), { class: "card-text" }, {
      default: withCtx(() => [
        renderSlot(t.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }));
  }
});
var Vo = ["id"];
var Ao = {
  key: 0,
  class: "carousel-indicators"
};
var xo = ["data-bs-target", "data-bs-slide-to", "aria-label"];
var Io = { class: "carousel-inner" };
var Fo = ["data-bs-target"];
var Po = createBaseVNode("span", {
  class: "carousel-control-prev-icon",
  "aria-hidden": "true"
}, null, -1);
var Oo = { class: "visually-hidden" };
var Lo = ["data-bs-target"];
var zo = createBaseVNode("span", {
  class: "carousel-control-next-icon",
  "aria-hidden": "true"
}, null, -1);
var No = { class: "visually-hidden" };
var ml = Symbol();
var Eo = defineComponent({
  __name: "BCarousel",
  props: {
    startingSlide: { default: 0 },
    id: null,
    imgHeight: null,
    imgWidth: null,
    background: null,
    modelValue: { default: 0 },
    controls: { default: false },
    indicators: { default: false },
    interval: { default: 5e3 },
    noTouch: { default: false },
    noWrap: { default: false },
    controlsPrevText: { default: "Previous" },
    controlsNextText: { default: "Next" },
    indicatorsButtonLabel: { default: "Slide" }
  },
  emits: ["sliding-start", "sliding-end"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = ke(toRef(a, "id"), "carousel"), u = r(toRef(a, "controls")), f = r(toRef(a, "indicators")), c = r(toRef(a, "noTouch"));
    r(toRef(a, "noWrap"));
    const v = ref(), p = ref(), B = ref([]);
    return Be(v, "slide.bs.carousel", (m) => t("sliding-start", m)), Be(v, "slid.bs.carousel", (m) => t("sliding-end", m)), onMounted(() => {
      p.value = new Carousel(v.value, {
        wrap: !c.value,
        interval: a.interval,
        touch: !c.value
      }), l.default && (B.value = l.default().filter((m) => {
        var $;
        return (($ = m.type) == null ? void 0 : $.__name) === "BCarouselSlide";
      }));
    }), provide(ml, {
      background: a.background,
      width: a.imgWidth,
      height: a.imgHeight
    }), (m, $) => (openBlock(), createElementBlock("div", {
      id: unref(n),
      ref_key: "element",
      ref: v,
      class: "carousel slide",
      "data-bs-ride": "carousel"
    }, [
      unref(f) ? (openBlock(), createElementBlock("div", Ao, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(B.value, (V, T) => (openBlock(), createElementBlock("button", {
          key: T,
          type: "button",
          "data-bs-target": `#${unref(n)}`,
          "data-bs-slide-to": T,
          class: normalizeClass(T === e.startingSlide ? "active" : ""),
          "aria-current": "true",
          "aria-label": `${e.indicatorsButtonLabel} ${T}`
        }, null, 10, xo))), 128))
      ])) : createCommentVNode("", true),
      createBaseVNode("div", Io, [
        renderSlot(m.$slots, "default")
      ]),
      unref(u) ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
        createBaseVNode("button", {
          class: "carousel-control-prev",
          type: "button",
          "data-bs-target": `#${unref(n)}`,
          "data-bs-slide": "prev"
        }, [
          Po,
          createBaseVNode("span", Oo, toDisplayString(e.controlsPrevText), 1)
        ], 8, Fo),
        createBaseVNode("button", {
          class: "carousel-control-next",
          type: "button",
          "data-bs-target": `#${unref(n)}`,
          "data-bs-slide": "next"
        }, [
          zo,
          createBaseVNode("span", No, toDisplayString(e.controlsNextText), 1)
        ], 8, Lo)
      ], 64)) : createCommentVNode("", true)
    ], 8, Vo));
  }
});
var Do = ["data-bs-interval"];
var Ho = ["innerHTML"];
var Ro = { key: 1 };
var qo = ["innerHTML"];
var Mo = { key: 1 };
var jo = defineComponent({
  __name: "BCarouselSlide",
  props: {
    imgSrc: null,
    imgHeight: null,
    imgWidth: null,
    interval: null,
    active: { default: false },
    background: null,
    caption: null,
    captionHtml: null,
    captionTag: { default: "h3" },
    contentTag: { default: "div" },
    contentVisibleUp: null,
    id: null,
    imgAlt: null,
    imgBlank: { default: false },
    imgBlankColor: { default: "transparent" },
    text: null,
    textHtml: null,
    textTag: { default: "p" }
  },
  setup(e) {
    const t = e, a = useSlots(), l = inject(ml, {}), n = r(toRef(t, "active")), u = computed(() => !Ve(a.default)), f = computed(() => ({
      background: `${t.background || l.background || "rgb(171, 171, 171)"} none repeat scroll 0% 0%`
    })), c = computed(() => ({
      "d-none": t.contentVisibleUp !== void 0,
      [`d-${t.contentVisibleUp}-block`]: t.contentVisibleUp !== void 0
    })), v = computed(() => l.width), p = computed(() => l.height);
    return (B, m) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["carousel-item", { active: unref(n) }]),
      "data-bs-interval": e.interval,
      style: normalizeStyle(unref(f))
    }, [
      renderSlot(B.$slots, "img", {}, () => [
        createVNode(Zt, {
          class: "d-block w-100",
          alt: e.imgAlt,
          src: e.imgSrc,
          width: e.imgWidth || unref(v),
          height: e.imgHeight || unref(p),
          blank: e.imgBlank,
          "blank-color": e.imgBlankColor
        }, null, 8, ["alt", "src", "width", "height", "blank", "blank-color"])
      ]),
      e.caption || e.captionHtml || e.text || e.textHtml || unref(u) ? (openBlock(), createBlock(resolveDynamicComponent(e.contentTag), {
        key: 0,
        class: normalizeClass(["carousel-caption", unref(c)])
      }, {
        default: withCtx(() => [
          e.caption || e.captionHtml ? (openBlock(), createBlock(resolveDynamicComponent(e.captionTag), { key: 0 }, {
            default: withCtx(() => [
              e.captionHtml ? (openBlock(), createElementBlock("span", {
                key: 0,
                innerHTML: e.captionHtml
              }, null, 8, Ho)) : (openBlock(), createElementBlock("span", Ro, toDisplayString(e.caption), 1))
            ]),
            _: 1
          })) : createCommentVNode("", true),
          e.text || e.textHtml ? (openBlock(), createBlock(resolveDynamicComponent(e.textTag), { key: 1 }, {
            default: withCtx(() => [
              e.textHtml ? (openBlock(), createElementBlock("span", {
                key: 0,
                innerHTML: e.textHtml
              }, null, 8, qo)) : (openBlock(), createElementBlock("span", Mo, toDisplayString(e.text), 1))
            ]),
            _: 1
          })) : createCommentVNode("", true),
          renderSlot(B.$slots, "default")
        ]),
        _: 3
      }, 8, ["class"])) : createCommentVNode("", true)
    ], 14, Do));
  }
});
var da = Bt("", [], { type: [Boolean, String, Number], default: false });
var ca = Bt("offset", [""], { type: [String, Number], default: null });
var fa = Bt("order", [""], { type: [String, Number], default: null });
var Go = defineComponent({
  name: "BCol",
  props: {
    col: { type: [Boolean, String], default: false },
    cols: { type: [String, Number], default: null },
    ...da,
    offset: { type: [String, Number], default: null },
    ...ca,
    order: { type: [String, Number], default: null },
    ...fa,
    alignSelf: { type: String, default: null },
    tag: { type: String, default: "div" }
  },
  setup(e) {
    const t = [
      { content: da, propPrefix: "cols", classPrefix: "col" },
      { content: ca, propPrefix: "offset" },
      { content: fa, propPrefix: "order" }
    ], a = r(toRef(e, "col")), l = computed(
      () => t.flatMap((u) => ja(e, u.content, u.propPrefix, u.classPrefix))
    );
    return {
      computedClasses: computed(() => [
        l.value,
        {
          col: a.value || !l.value.some((u) => /^col-/.test(u)) && !e.cols,
          [`col-${e.cols}`]: !!e.cols,
          [`offset-${e.offset}`]: !!e.offset,
          [`order-${e.order}`]: !!e.order,
          [`align-self-${e.alignSelf}`]: !!e.alignSelf
        }
      ])
    };
  }
});
function Uo(e, t, a, l, n, u) {
  return openBlock(), createBlock(resolveDynamicComponent(e.tag), {
    class: normalizeClass(e.computedClasses)
  }, {
    default: withCtx(() => [
      renderSlot(e.$slots, "default")
    ]),
    _: 3
  }, 8, ["class"]);
}
var et = _e(Go, [["render", Uo]]);
var We = {
  autoHide: true,
  delay: 5e3,
  noCloseButton: false,
  pos: "top-right",
  value: true
};
var va = class {
  constructor(t) {
    be(this, "vm");
    be(this, "containerPositions");
    isReactive(t) ? this.vm = t : this.vm = reactive(t), this.containerPositions = computed(() => {
      const a = /* @__PURE__ */ new Set([]);
      return this.vm.toasts.map((l) => {
        l.options.pos && a.add(l.options.pos);
      }), a;
    });
  }
  toasts(t) {
    return t ? computed(
      () => this.vm.toasts.filter((a) => {
        if (a.options.pos === t && a.options.value)
          return a;
      })
    ) : computed(() => this.vm.toasts);
  }
  remove(...t) {
    this.vm.toasts = this.vm.toasts.filter((a) => {
      if (a.options.id && !t.includes(a.options.id))
        return a;
    });
  }
  isRoot() {
    var t;
    return (t = this.vm.root) != null ? t : false;
  }
  show(t, a = We) {
    const l = { id: Ee(), ...We, ...a }, n = {
      options: reactive(l),
      content: t
    };
    return this.vm.toasts.push(n), n;
  }
  info(t, a = We) {
    return this.show(t, { variant: "info", ...a });
  }
  danger(t, a = We) {
    return this.show(t, { variant: "danger", ...a });
  }
  warning(t, a = We) {
    return this.show(t, { variant: "warning", ...a });
  }
  success(t, a = We) {
    return this.show(t, { variant: "success", ...a });
  }
  hide() {
  }
};
var Wo = class {
  constructor() {
    be(this, "vms");
    be(this, "rootInstance");
    be(this, "useToast", gl);
    this.vms = {};
  }
  getOrCreateViewModel(t) {
    if (!t) {
      if (this.rootInstance)
        return this.vms[this.rootInstance];
      const a = { root: true, toasts: [], container: void 0, id: Symbol("toast") };
      return this.rootInstance = a.id, this.vms[a.id] = a, a;
    }
    if (t.root) {
      if (this.rootInstance)
        return this.vms[this.rootInstance];
      this.rootInstance = t.id;
    }
    return this.vms[t.id] = t, t;
  }
  getVM(t) {
    if (!t && this.rootInstance)
      return this.vms[this.rootInstance];
    if (t)
      return this.vms[t];
  }
};
var Nt = Symbol();
var bl = Symbol();
var Ko = {
  container: void 0,
  toasts: [],
  root: false
};
function Xo() {
  return inject(bl);
}
function gl(e, t = Nt) {
  const a = inject(Xo());
  if (!e)
    return new va(a.getOrCreateViewModel());
  const l = { id: Symbol("toastInstance") }, n = { ...Ko, ...l, ...e }, u = a.getOrCreateViewModel(n);
  return new va(u);
}
var Jo = {
  install: (e, t = {}) => {
    var a, l, n, u;
    e.provide(bl, (l = (a = t == null ? void 0 : t.BToast) == null ? void 0 : a.injectkey) != null ? l : Nt), e.provide((u = (n = t == null ? void 0 : t.BToast) == null ? void 0 : n.injectkey) != null ? u : Nt, new Wo());
  }
};
var St = defineComponent({
  __name: "BTransition",
  props: {
    appear: { default: false },
    mode: null,
    noFade: { default: false },
    transProps: null
  },
  setup(e) {
    const t = e, a = r(toRef(t, "appear")), l = r(toRef(t, "noFade")), n = computed(() => {
      const c = {
        name: "",
        enterActiveClass: "",
        enterToClass: "",
        leaveActiveClass: "",
        leaveToClass: "showing",
        enterFromClass: "showing",
        leaveFromClass: ""
      }, v = {
        ...c,
        enterActiveClass: "fade showing",
        leaveActiveClass: "fade showing"
      };
      return l.value ? c : v;
    }), u = computed(() => ({ mode: t.mode, css: true, ...n.value })), f = computed(
      () => t.transProps !== void 0 ? {
        ...u.value,
        ...t.transProps
      } : a.value ? {
        ...u.value,
        appear: true,
        appearActiveClass: n.value.enterActiveClass,
        appearToClass: n.value.enterToClass
      } : u.value
    );
    return (c, v) => (openBlock(), createBlock(Transition, normalizeProps(guardReactiveProps(unref(f))), {
      default: withCtx(() => [
        renderSlot(c.$slots, "default")
      ]),
      _: 3
    }, 16));
  }
});
var Yo = "toast-title";
var ma = 1e3;
var pl = defineComponent({
  components: { BLink: Ae },
  props: {
    ...Ge,
    delay: { type: Number, default: 5e3 },
    bodyClass: { type: String },
    body: { type: [Object, String] },
    headerClass: { type: String },
    headerTag: { type: String, default: "div" },
    animation: { type: [Boolean, String], default: true },
    id: { type: String },
    isStatus: { type: [Boolean, String], default: false },
    autoHide: { type: [Boolean, String], default: true },
    noCloseButton: { type: [Boolean, String], default: false },
    noFade: { type: [Boolean, String], default: false },
    noHoverPause: { type: [Boolean, String], default: false },
    solid: { type: [Boolean, String], default: false },
    static: { type: [Boolean, String], default: false },
    title: { type: String },
    modelValue: { type: [Boolean, String], default: false },
    toastClass: { type: Array },
    variant: { type: String }
  },
  emits: ["destroyed", "update:modelValue"],
  setup(e, { emit: t, slots: a }) {
    r(toRef(e, "animation"));
    const l = r(toRef(e, "isStatus")), n = r(toRef(e, "autoHide")), u = r(toRef(e, "noCloseButton")), f = r(toRef(e, "noFade")), c = r(toRef(e, "noHoverPause"));
    r(toRef(e, "solid")), r(toRef(e, "static"));
    const v = r(toRef(e, "modelValue")), p = ref(false), B = ref(false), m = ref(false), $ = computed(() => ({
      [`b-toast-${e.variant}`]: e.variant !== void 0,
      show: m.value || p.value
    }));
    let V, T, b;
    const k = () => {
      typeof V > "u" || (clearTimeout(V), V = void 0);
    }, y = computed(
      () => Math.max(Le(e.delay, 0), ma)
    ), C = () => {
      v.value && (T = b = 0, k(), B.value = true, it(() => {
        m.value = false;
      }));
    }, q = () => {
      k(), t("update:modelValue", true), T = b = 0, B.value = false, nextTick(() => {
        it(() => {
          m.value = true;
        });
      });
    }, E = () => {
      if (!n.value || c.value || !V || b)
        return;
      const te = Date.now() - T;
      te > 0 && (k(), b = Math.max(y.value - te, ma));
    }, I = () => {
      (!n.value || c.value || !b) && (b = T = 0), _();
    };
    watch(
      () => v.value,
      (te) => {
        te ? q() : C();
      }
    );
    const _ = () => {
      k(), n.value && (V = setTimeout(C, b || y.value), T = Date.now(), b = 0);
    }, z = () => {
      p.value = true, t("update:modelValue", true);
    }, x = () => {
      p.value = false, _();
    }, w = () => {
      p.value = true;
    }, P = () => {
      p.value = false, b = T = 0, t("update:modelValue", false);
    };
    onUnmounted(() => {
      k(), n.value && t("destroyed", e.id);
    }), onMounted(() => {
      nextTick(() => {
        v.value && it(() => {
          q();
        });
      });
    });
    const L = () => {
      nextTick(() => {
        it(() => {
          C();
        });
      });
    };
    return () => {
      const te = () => {
        const Q = [], ie = Fe(Yo, { hide: C }, a);
        ie ? Q.push(h(ie)) : e.title && Q.push(h("strong", { class: "me-auto" }, e.title)), !u.value && Q.length !== 0 && Q.push(
          h(Ze, {
            class: ["btn-close"],
            onClick: () => {
              C();
            }
          })
        );
        const K = [];
        if (Q.length > 0 && K.push(
          h(
            e.headerTag,
            {
              class: "toast-header"
            },
            { default: () => Q }
          )
        ), Fe("default", { hide: C }, a) || e.body) {
          const me = h(
            at(e) ? "b-link" : "div",
            {
              class: ["toast-body", e.bodyClass],
              onClick: at(e) ? { click: L } : {}
            },
            Fe("default", { hide: C }, a) || e.body
          );
          K.push(me);
        }
        return h(
          "div",
          {
            class: ["toast", e.toastClass, $.value],
            tabindex: "0"
          },
          K
        );
      };
      return h(
        "div",
        {
          class: ["b-toast"],
          id: e.id,
          role: B.value ? null : l.value ? "status" : "alert",
          "aria-live": B.value ? null : l.value ? "polite" : "assertive",
          "aria-atomic": B.value ? null : "true",
          onmouseenter: E,
          onmouseleave: I
        },
        [
          h(
            St,
            {
              noFade: f.value,
              onAfterEnter: x,
              onBeforeEnter: z,
              onAfterLeave: P,
              onBeforeLeave: w
            },
            () => [m.value ? te() : ""]
          )
        ]
      );
    };
  }
});
var Et = defineComponent({
  __name: "BToaster",
  props: {
    position: { default: "top-right" },
    instance: null
  },
  setup(e) {
    const t = e, a = {
      "top-left": "top-0 start-0",
      "top-center": "top-0 start-50 translate-middle-x",
      "top-right": "top-0 end-0",
      "middle-left": "top-50 start-0 translate-middle-y",
      "middle-center": "top-50 start-50 translate-middle",
      "middle-right": "top-50 end-0 translate-middle-y",
      "bottom-left": "bottom-0 start-0",
      "bottom-center": "bottom-0 start-50 translate-middle-x",
      "bottom-right": "bottom-0 end-0"
    }, l = computed(() => a[t.position]), n = (u) => {
      var f;
      (f = t.instance) == null || f.remove(u);
    };
    return (u, f) => {
      var c;
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([[unref(l)], "b-toaster position-fixed p-3"]),
        style: { "z-index": "11" }
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList((c = e.instance) == null ? void 0 : c.toasts(e.position).value, (v) => (openBlock(), createBlock(pl, {
          id: v.options.id,
          key: v.options.id,
          modelValue: v.options.value,
          "onUpdate:modelValue": (p) => v.options.value = p,
          "auto-hide": v.options.autoHide,
          delay: v.options.delay,
          "no-close-button": v.options.noCloseButton,
          title: v.content.title,
          body: v.content.body,
          component: v.content.body,
          variant: v.options.variant,
          onDestroyed: n
        }, null, 8, ["id", "modelValue", "onUpdate:modelValue", "auto-hide", "delay", "no-close-button", "title", "body", "component", "variant"]))), 128))
      ], 2);
    };
  }
});
var Zo = defineComponent({
  props: {
    gutterX: { type: String, default: null },
    gutterY: { type: String, default: null },
    fluid: { type: [Boolean, String], default: false },
    toast: { type: Object },
    position: { type: String, required: false }
  },
  setup(e, { slots: t, expose: a }) {
    const l = ref();
    let n;
    const u = computed(() => ({
      container: !e.fluid,
      ["container-fluid"]: typeof e.fluid == "boolean" && e.fluid,
      [`container-${e.fluid}`]: typeof e.fluid == "string",
      [`gx-${e.gutterX}`]: e.gutterX !== null,
      [`gy-${e.gutterY}`]: e.gutterY !== null
    }));
    return onMounted(() => {
      e.toast;
    }), e.toast && (n = gl({ container: l, root: e.toast.root }), a({})), () => {
      var c;
      const f = [];
      return n == null || n.containerPositions.value.forEach((v) => {
        f.push(h(Et, { key: v, instance: n, position: v }));
      }), h("div", { class: [u.value, e.position], ref: l }, [
        ...f,
        (c = t.default) == null ? void 0 : c.call(t)
      ]);
    };
  },
  methods: {}
});
var Qo = { class: "visually-hidden" };
var es = ["aria-labelledby", "role"];
var hl = defineComponent({
  __name: "BDropdown",
  props: {
    id: null,
    menuClass: null,
    size: null,
    splitClass: null,
    splitVariant: null,
    text: null,
    toggleClass: null,
    autoClose: { type: [Boolean, String], default: true },
    block: { default: false },
    boundary: { default: "clippingParents" },
    dark: { default: false },
    disabled: { default: false },
    isNav: { default: false },
    dropup: { default: false },
    dropright: { default: false },
    dropleft: { default: false },
    noFlip: { default: false },
    offset: { default: 0 },
    popperOpts: { default: () => ({}) },
    right: { default: false },
    role: { default: "menu" },
    split: { default: false },
    splitButtonType: { default: "button" },
    splitHref: { default: void 0 },
    noCaret: { default: false },
    toggleText: { default: "Toggle dropdown" },
    variant: { default: "secondary" }
  },
  emits: ["show", "shown", "hide", "hidden", "click", "toggle"],
  setup(e, { expose: t, emit: a }) {
    const l = e, n = ke(toRef(l, "id"), "dropdown"), u = r(toRef(l, "block")), f = r(toRef(l, "dark")), c = r(toRef(l, "dropup")), v = r(toRef(l, "dropright")), p = r(toRef(l, "isNav")), B = r(toRef(l, "dropleft")), m = r(toRef(l, "right")), $ = r(toRef(l, "split")), V = r(toRef(l, "noCaret")), T = ref(), b = ref(), k = ref(), y = computed(() => ({
      "d-grid": u.value,
      "d-flex": u.value && $.value
    })), C = computed(() => [
      $.value ? l.splitClass : l.toggleClass,
      {
        "nav-link": p.value,
        "dropdown-toggle": !$.value,
        "dropdown-toggle-no-caret": V.value && !$.value,
        "w-100": $.value && u.value
      }
    ]), q = computed(() => [
      l.menuClass,
      {
        "dropdown-menu-dark": f.value,
        "dropdown-menu-end": m.value
      }
    ]), E = computed(() => ({
      "data-bs-toggle": $.value ? void 0 : "dropdown",
      "aria-expanded": $.value ? void 0 : false,
      ref: $.value ? void 0 : b,
      href: $.value ? l.splitHref : void 0
    })), I = computed(() => ({
      ref: $.value ? b : void 0
    })), _ = () => {
      var x;
      (x = k.value) == null || x.hide();
    }, z = (x) => {
      $.value && a("click", x);
    };
    return Be(T, "show.bs.dropdown", () => a("show")), Be(T, "shown.bs.dropdown", () => a("shown")), Be(T, "hide.bs.dropdown", () => a("hide")), Be(T, "hidden.bs.dropdown", () => a("hidden")), onMounted(() => {
      var x;
      k.value = new Dropdown((x = b.value) == null ? void 0 : x.$el, {
        autoClose: l.autoClose,
        boundary: l.boundary,
        offset: l.offset ? l.offset.toString() : "",
        reference: l.offset || $.value ? "parent" : "toggle",
        popperConfig: (w) => {
          const P = {
            placement: "bottom-start",
            modifiers: l.noFlip ? [
              {
                name: "flip",
                options: {
                  fallbackPlacements: []
                }
              }
            ] : []
          };
          return c.value ? P.placement = m.value ? "top-end" : "top-start" : v.value ? P.placement = "right-start" : B.value ? P.placement = "left-start" : m.value && (P.placement = "bottom-end"), Ot(w, Ot(P, l.popperOpts));
        }
      });
    }), t({
      hide: _
    }), (x, w) => (openBlock(), createElementBlock("div", {
      ref_key: "parent",
      ref: T,
      class: normalizeClass([unref(y), "btn-group"])
    }, [
      createVNode(lt, mergeProps({
        id: unref(n),
        variant: e.splitVariant || e.variant,
        size: e.size,
        class: unref(C),
        disabled: e.disabled,
        type: e.splitButtonType
      }, unref(E), { onClick: z }), {
        default: withCtx(() => [
          renderSlot(x.$slots, "button-content", {}, () => [
            createTextVNode(toDisplayString(e.text), 1)
          ])
        ]),
        _: 3
      }, 16, ["id", "variant", "size", "class", "disabled", "type"]),
      unref($) ? (openBlock(), createBlock(lt, mergeProps({
        key: 0,
        variant: e.variant,
        size: e.size,
        disabled: e.disabled
      }, unref(I), {
        class: [e.toggleClass, "dropdown-toggle-split dropdown-toggle"],
        "data-bs-toggle": "dropdown",
        "aria-expanded": "false",
        onClick: w[0] || (w[0] = (P) => a("toggle"))
      }), {
        default: withCtx(() => [
          createBaseVNode("span", Qo, [
            renderSlot(x.$slots, "toggle-text", {}, () => [
              createTextVNode(toDisplayString(e.toggleText), 1)
            ])
          ])
        ]),
        _: 3
      }, 16, ["variant", "size", "disabled", "class"])) : createCommentVNode("", true),
      createBaseVNode("ul", {
        class: normalizeClass(["dropdown-menu", unref(q)]),
        "aria-labelledby": unref(n),
        role: e.role
      }, [
        renderSlot(x.$slots, "default")
      ], 10, es)
    ], 2));
  }
});
var ts = { role: "presentation" };
var as = defineComponent({
  __name: "BDropdownDivider",
  props: {
    tag: { default: "hr" }
  },
  setup(e) {
    return (t, a) => (openBlock(), createElementBlock("li", ts, [
      (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
        class: "dropdown-divider",
        role: "separator",
        "aria-orientation": "horizontal"
      }))
    ]));
  }
});
var ls = {};
var ns = { role: "presentation" };
var os = { class: "px-4 py-3" };
function ss(e, t) {
  return openBlock(), createElementBlock("li", ns, [
    createBaseVNode("form", os, [
      renderSlot(e.$slots, "default")
    ])
  ]);
}
var is = _e(ls, [["render", ss]]);
var rs = { role: "presentation" };
var us = ["id", "aria-describedby"];
var ds = {
  inheritAttrs: false
};
var cs = defineComponent({
  ...ds,
  __name: "BDropdownGroup",
  props: {
    id: null,
    ariaDescribedby: null,
    header: null,
    headerClass: null,
    headerTag: { default: "header" },
    headerVariant: null
  },
  setup(e) {
    const t = e, a = computed(
      () => t.id ? `${t.id}_group_dd_header` : void 0
    ), l = computed(
      () => t.headerTag === "header" ? void 0 : "heading"
    ), n = computed(() => [
      t.headerClass,
      {
        [`text-${t.headerVariant}`]: t.headerVariant !== void 0
      }
    ]);
    return (u, f) => (openBlock(), createElementBlock("li", rs, [
      (openBlock(), createBlock(resolveDynamicComponent(e.headerTag), {
        id: unref(a),
        class: normalizeClass(["dropdown-header", unref(n)]),
        role: unref(l)
      }, {
        default: withCtx(() => [
          renderSlot(u.$slots, "header", {}, () => [
            createTextVNode(toDisplayString(e.header), 1)
          ])
        ]),
        _: 3
      }, 8, ["id", "class", "role"])),
      createBaseVNode("ul", mergeProps({
        id: e.id,
        role: "group",
        class: "list-unstyled"
      }, u.$attrs, {
        "aria-describedby": e.ariaDescribedby || unref(a)
      }), [
        renderSlot(u.$slots, "default")
      ], 16, us)
    ]));
  }
});
var fs = {};
var vs = { class: "dropdown-header" };
function ms(e, t) {
  return openBlock(), createElementBlock("li", null, [
    createBaseVNode("h6", vs, [
      renderSlot(e.$slots, "default")
    ])
  ]);
}
var bs = _e(fs, [["render", ms]]);
var gs = {
  inheritAttrs: false
};
var ps = defineComponent({
  ...gs,
  __name: "BDropdownItem",
  props: {
    href: null,
    linkClass: null,
    active: { default: false },
    disabled: { default: false },
    rel: { default: void 0 },
    target: { default: "_self" },
    variant: null
  },
  emits: ["click"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "active")), n = r(toRef(a, "disabled")), u = useAttrs(), f = computed(() => [
      a.linkClass,
      {
        active: l.value,
        disabled: n.value,
        [`text-${a.variant}`]: a.variant !== void 0
      }
    ]), c = computed(
      () => a.href ? "a" : u.to ? Ae : "button"
    ), v = computed(() => ({
      disabled: n.value,
      "aria-current": l.value ? "true" : null,
      href: c.value === "a" ? a.href : null,
      rel: a.rel,
      type: c.value === "button" ? "button" : null,
      target: a.target,
      ...u.to ? { activeClass: "active", ...u } : {}
    })), p = (B) => t("click", B);
    return (B, m) => (openBlock(), createElementBlock("li", {
      role: "presentation",
      class: normalizeClass(B.$attrs.class)
    }, [
      (openBlock(), createBlock(resolveDynamicComponent(unref(c)), mergeProps({
        class: ["dropdown-item", unref(f)]
      }, unref(v), { onClick: p }), {
        default: withCtx(() => [
          renderSlot(B.$slots, "default")
        ]),
        _: 3
      }, 16, ["class"]))
    ], 2));
  }
});
var hs = ["disabled"];
var ys = {
  inheritAttrs: false
};
var Bs = defineComponent({
  ...ys,
  __name: "BDropdownItemButton",
  props: {
    buttonClass: null,
    active: { default: false },
    activeClass: { default: "active" },
    disabled: { default: false },
    variant: null
  },
  emits: ["click"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "active")), n = r(toRef(a, "disabled")), u = computed(() => [
      a.buttonClass,
      {
        [a.activeClass]: l.value,
        disabled: n.value,
        [`text-${a.variant}`]: a.variant !== void 0
      }
    ]), f = (c) => t("click", c);
    return (c, v) => (openBlock(), createElementBlock("li", {
      role: "presentation",
      class: normalizeClass(c.$attrs.class)
    }, [
      createBaseVNode("button", {
        role: "menu",
        type: "button",
        class: normalizeClass(["dropdown-item", unref(u)]),
        disabled: unref(n),
        onClick: f
      }, [
        renderSlot(c.$slots, "default")
      ], 10, hs)
    ], 2));
  }
});
var $s = { role: "presentation" };
var ks = { class: "px-4 py-1 mb-0 text-muted" };
var Cs = defineComponent({
  __name: "BDropdownText",
  props: {
    text: { default: "" }
  },
  setup(e) {
    return (t, a) => (openBlock(), createElementBlock("li", $s, [
      createBaseVNode("p", ks, [
        renderSlot(t.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ])
    ]));
  }
});
var Ss = ["id", "novalidate", "onSubmit"];
var yl = defineComponent({
  __name: "BForm",
  props: {
    id: null,
    floating: { default: false },
    novalidate: { default: false },
    validated: { default: false }
  },
  emits: ["submit"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "floating")), n = r(toRef(a, "novalidate")), u = r(toRef(a, "validated")), f = computed(() => ({
      "form-floating": l.value,
      "was-validated": u.value
    })), c = (v) => t("submit", v);
    return (v, p) => (openBlock(), createElementBlock("form", {
      id: e.id,
      novalidate: unref(n),
      class: normalizeClass(unref(f)),
      onSubmit: withModifiers(c, ["prevent"])
    }, [
      renderSlot(v.$slots, "default")
    ], 42, Ss));
  }
});
var ws = { class: "form-floating" };
var Ts = ["for"];
var _s = defineComponent({
  __name: "BFormFloatingLabel",
  props: {
    labelFor: null,
    label: null,
    text: null
  },
  setup(e) {
    return (t, a) => (openBlock(), createElementBlock("div", ws, [
      renderSlot(t.$slots, "default", {}, () => [
        createTextVNode(toDisplayString(e.text), 1)
      ]),
      createBaseVNode("label", { for: e.labelFor }, [
        renderSlot(t.$slots, "label", {}, () => [
          createTextVNode(toDisplayString(e.label), 1)
        ])
      ], 8, Ts)
    ]));
  }
});
var Dt = defineComponent({
  __name: "BFormInvalidFeedback",
  props: {
    ariaLive: null,
    forceShow: { default: false },
    id: null,
    text: null,
    role: null,
    state: { default: void 0 },
    tag: { default: "div" },
    tooltip: { default: false }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "forceShow")), l = r(toRef(t, "state")), n = r(toRef(t, "tooltip")), u = computed(
      () => a.value === true || l.value === false
    ), f = computed(() => ({
      "d-block": u.value,
      "invalid-feedback": !n.value,
      "invalid-tooltip": n.value
    })), c = computed(() => ({
      id: t.id,
      role: t.role,
      "aria-live": t.ariaLive,
      "aria-atomic": t.ariaLive ? "true" : void 0
    }));
    return (v, p) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), mergeProps({ class: unref(f) }, unref(c)), {
      default: withCtx(() => [
        renderSlot(v.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 16, ["class"]));
  }
});
var ft = defineComponent({
  __name: "BFormRow",
  props: {
    tag: { default: "div" }
  },
  setup(e) {
    return (t, a) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), { class: "row d-flex flex-wrap" }, {
      default: withCtx(() => [
        renderSlot(t.$slots, "default")
      ]),
      _: 3
    }));
  }
});
var Ht = defineComponent({
  __name: "BFormText",
  props: {
    id: null,
    inline: { default: false },
    tag: { default: "small" },
    text: null,
    textVariant: { default: "muted" }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "inline")), l = computed(() => [
      [`text-${t.textVariant}`],
      {
        "form-text": !a.value
      }
    ]);
    return (n, u) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: e.id,
      class: normalizeClass(unref(l))
    }, {
      default: withCtx(() => [
        renderSlot(n.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 8, ["id", "class"]));
  }
});
var Rt = defineComponent({
  __name: "BFormValidFeedback",
  props: {
    ariaLive: null,
    forceShow: { default: false },
    id: null,
    role: null,
    text: null,
    state: { default: void 0 },
    tag: { default: "div" },
    tooltip: { default: false }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "forceShow")), l = r(toRef(t, "state")), n = r(toRef(t, "tooltip")), u = computed(
      () => a.value === true || l.value === true
    ), f = computed(() => ({
      "d-block": u.value,
      "valid-feedback": !n.value,
      "valid-tooltip": n.value
    })), c = computed(() => t.ariaLive ? "true" : void 0);
    return (v, p) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: e.id,
      role: e.role,
      "aria-live": e.ariaLive,
      "aria-atomic": unref(c),
      class: normalizeClass(unref(f))
    }, {
      default: withCtx(() => [
        renderSlot(v.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }, 8, ["id", "role", "aria-live", "aria-atomic", "class"]));
  }
});
var Vs = ["id", "disabled", "required", "name", "form", "aria-label", "aria-labelledby", "aria-required", "value", "indeterminate"];
var As = ["for"];
var xs = {
  inheritAttrs: false
};
var Bl = defineComponent({
  ...xs,
  __name: "BFormCheckbox",
  props: {
    ariaLabel: null,
    ariaLabelledBy: null,
    form: null,
    indeterminate: null,
    name: null,
    id: { default: void 0 },
    autofocus: { default: false },
    plain: { default: false },
    button: { default: false },
    switch: { default: false },
    disabled: { default: false },
    buttonVariant: { default: "secondary" },
    inline: { default: false },
    required: { default: void 0 },
    size: { default: "md" },
    state: { default: void 0 },
    uncheckedValue: { type: [Array, Set, Boolean, String, Object, Number], default: false },
    value: { type: [Array, Set, Boolean, String, Object, Number], default: true },
    modelValue: { type: [Array, Set, Boolean, String, Object, Number], default: void 0 }
  },
  emits: ["update:modelValue", "input", "change"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = ke(toRef(a, "id"), "form-check"), u = r(toRef(a, "indeterminate")), f = r(toRef(a, "autofocus")), c = r(toRef(a, "plain")), v = r(toRef(a, "button")), p = r(toRef(a, "switch")), B = r(toRef(a, "disabled")), m = r(toRef(a, "inline")), $ = r(toRef(a, "required")), V = r(toRef(a, "state")), T = ref(null), b = ref(false), k = computed(() => !Ve(l.default)), y = computed({
      get: () => a.uncheckedValue ? Array.isArray(a.modelValue) ? a.modelValue.indexOf(a.value) > -1 : a.modelValue === a.value : a.modelValue,
      set: (z) => {
        let x = z;
        Array.isArray(a.modelValue) ? a.uncheckedValue && (x = a.modelValue, z ? (x.indexOf(a.uncheckedValue) > -1 && x.splice(x.indexOf(a.uncheckedValue), 1), x.push(a.value)) : (x.indexOf(a.value) > -1 && x.splice(x.indexOf(a.value), 1), x.push(a.uncheckedValue))) : x = z ? a.value : a.uncheckedValue, t("input", x), t("update:modelValue", x), t("change", x);
      }
    }), C = computed(() => Array.isArray(a.modelValue) ? a.modelValue.indexOf(a.value) > -1 : JSON.stringify(a.modelValue) === JSON.stringify(a.value)), q = reactive({
      plain: toRef(c, "value"),
      button: toRef(v, "value"),
      inline: toRef(m, "value"),
      switch: toRef(p, "value"),
      size: toRef(a, "size"),
      state: toRef(V, "value"),
      buttonVariant: toRef(a, "buttonVariant")
    }), E = Wa(q), I = Ka(q), _ = Xa(q);
    return onMounted(() => {
      f.value && T.value.focus();
    }), (z, x) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(unref(E))
    }, [
      withDirectives(createBaseVNode("input", mergeProps({ id: unref(n) }, z.$attrs, {
        ref_key: "input",
        ref: T,
        "onUpdate:modelValue": x[0] || (x[0] = (w) => isRef(y) ? y.value = w : null),
        class: unref(I),
        type: "checkbox",
        disabled: unref(B),
        required: !!e.name && !!unref($),
        name: e.name,
        form: e.form,
        "aria-label": e.ariaLabel,
        "aria-labelledby": e.ariaLabelledBy,
        "aria-required": e.name && unref($) ? "true" : void 0,
        value: e.value,
        indeterminate: unref(u),
        onFocus: x[1] || (x[1] = (w) => b.value = true),
        onBlur: x[2] || (x[2] = (w) => b.value = false)
      }), null, 16, Vs), [
        [vModelCheckbox, unref(y)]
      ]),
      unref(k) || !unref(c) ? (openBlock(), createElementBlock("label", {
        key: 0,
        for: unref(n),
        class: normalizeClass([unref(_), { active: unref(C), focus: b.value }])
      }, [
        renderSlot(z.$slots, "default")
      ], 10, As)) : createCommentVNode("", true)
    ], 2));
  }
});
var Is = ["id"];
var Fs = ["innerHTML"];
var Ps = ["textContent"];
var Os = defineComponent({
  __name: "BFormCheckboxGroup",
  props: {
    id: null,
    form: null,
    modelValue: { default: () => [] },
    ariaInvalid: { default: void 0 },
    autofocus: { default: false },
    buttonVariant: { default: "secondary" },
    buttons: { default: false },
    disabled: { default: false },
    disabledField: { default: "disabled" },
    htmlField: { default: "html" },
    name: null,
    options: { default: () => [] },
    plain: { default: false },
    required: { default: false },
    size: null,
    stacked: { default: false },
    state: { default: void 0 },
    switches: { default: false },
    textField: { default: "text" },
    validated: { default: false },
    valueField: { default: "value" }
  },
  emits: ["input", "update:modelValue", "change"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = "BFormCheckbox", u = ke(toRef(a, "id"), "checkbox"), f = ke(toRef(a, "name"), "checkbox");
    r(toRef(a, "autofocus"));
    const c = r(toRef(a, "buttons")), v = r(toRef(a, "disabled"));
    r(toRef(a, "plain"));
    const p = r(toRef(a, "required")), B = r(toRef(a, "stacked")), m = r(toRef(a, "state")), $ = r(toRef(a, "switches")), V = r(toRef(a, "validated")), T = computed({
      get: () => a.modelValue,
      set: (q) => {
        if (JSON.stringify(q) === JSON.stringify(a.modelValue))
          return;
        const E = a.options.filter(
          (I) => q.map((_) => JSON.stringify(_)).includes(JSON.stringify(typeof I == "string" ? I : I[a.valueField]))
        ).map((I) => typeof I == "string" ? I : I[a.valueField]);
        t("input", E), t("update:modelValue", E), t("change", E);
      }
    }), b = computed(
      () => (l.first ? gt(l.first(), n, v.value) : []).concat(a.options.map((q) => Za(q, a))).concat(l.default ? gt(l.default(), n, v.value) : []).map((q, E) => Qa(q, E, a, f, u)).map((q) => ({
        ...q,
        props: {
          switch: $.value,
          ...q.props
        }
      }))
    ), k = reactive({
      required: toRef(p, "value"),
      ariaInvalid: toRef(a, "ariaInvalid"),
      state: toRef(m, "value"),
      validated: toRef(V, "value"),
      buttons: toRef(c, "value"),
      stacked: toRef(B, "value"),
      size: toRef(a, "size")
    }), y = Ja(k), C = Ya(k);
    return (q, E) => (openBlock(), createElementBlock("div", mergeProps(unref(y), {
      id: unref(u),
      role: "group",
      class: [unref(C), "bv-no-focus-ring"],
      tabindex: "-1"
    }), [
      (openBlock(true), createElementBlock(Fragment, null, renderList(unref(b), (I, _) => (openBlock(), createBlock(Bl, mergeProps({
        key: _,
        modelValue: unref(T),
        "onUpdate:modelValue": E[0] || (E[0] = (z) => isRef(T) ? T.value = z : null)
      }, I.props), {
        default: withCtx(() => [
          I.html ? (openBlock(), createElementBlock("span", {
            key: 0,
            innerHTML: I.html
          }, null, 8, Fs)) : (openBlock(), createElementBlock("span", {
            key: 1,
            textContent: toDisplayString(I.text)
          }, null, 8, Ps))
        ]),
        _: 2
      }, 1040, ["modelValue"]))), 128))
    ], 16, Is));
  }
});
var $l = ["input", "select", "textarea"];
var Ls = $l.map((e) => `${e}:not([disabled])`).join();
var zs = [...$l, "a", "button", "label"];
var Ns = "label";
var Es = "invalid-feedback";
var Ds = "valid-feedback";
var Hs = "description";
var Rs = "default";
var qs = defineComponent({
  components: { BCol: et, BFormInvalidFeedback: Dt, BFormRow: ft, BFormText: Ht, BFormValidFeedback: Rt },
  props: {
    contentCols: { type: [Boolean, String, Number], required: false },
    contentColsLg: { type: [Boolean, String, Number], required: false },
    contentColsMd: { type: [Boolean, String, Number], required: false },
    contentColsSm: { type: [Boolean, String, Number], required: false },
    contentColsXl: { type: [Boolean, String, Number], required: false },
    description: { type: [String], required: false },
    disabled: { type: [Boolean, String], default: false },
    feedbackAriaLive: { type: String, default: "assertive" },
    id: { type: String, required: false },
    invalidFeedback: { type: String, required: false },
    label: { type: String, required: false },
    labelAlign: { type: [Boolean, String, Number], required: false },
    labelAlignLg: { type: [Boolean, String, Number], required: false },
    labelAlignMd: { type: [Boolean, String, Number], required: false },
    labelAlignSm: { type: [Boolean, String, Number], required: false },
    labelAlignXl: { type: [Boolean, String, Number], required: false },
    labelClass: { type: [Array, Object, String], required: false },
    labelCols: { type: [Boolean, String, Number], required: false },
    labelColsLg: { type: [Boolean, String, Number], required: false },
    labelColsMd: { type: [Boolean, String, Number], required: false },
    labelColsSm: { type: [Boolean, String, Number], required: false },
    labelColsXl: { type: [Boolean, String, Number], required: false },
    labelFor: { type: String, required: false },
    labelSize: { type: String, required: false },
    labelSrOnly: { type: [Boolean, String], default: false },
    state: { type: [Boolean, String], default: null },
    tooltip: { type: [Boolean, String], default: false },
    validFeedback: { type: String, required: false },
    validated: { type: [Boolean, String], default: false },
    floating: { type: [Boolean, String], default: false }
  },
  setup(e, { attrs: t }) {
    const a = r(toRef(e, "disabled")), l = r(toRef(e, "labelSrOnly")), n = r(toRef(e, "state")), u = r(toRef(e, "tooltip")), f = r(toRef(e, "validated")), c = r(toRef(e, "floating")), v = null, p = ["xs", "sm", "md", "lg", "xl"], B = (_, z) => p.reduce((x, w) => {
      const P = oa(w === "xs" ? "" : w, `${z}Align`), L = _[P] || null;
      return L && (w === "xs" ? x.push(`text-${L}`) : x.push(`text-${w}-${L}`)), x;
    }, []), m = (_, z) => p.reduce((x, w) => {
      const P = oa(w === "xs" ? "" : w, `${z}Cols`);
      let L = _[P];
      return L = L === "" ? true : L || false, typeof L != "boolean" && L !== "auto" && (L = Qe(L, 0), L = L > 0 ? L : false), L && (w === "xs" ? x.cols = L : x[w || (typeof L == "boolean" ? "col" : "cols")] = L), x;
    }, {}), $ = ref(), V = (_, z = null) => {
      if (za && e.labelFor) {
        const x = qa(`#${bn(e.labelFor)}`, $);
        if (x) {
          const w = "aria-describedby", P = (_ || "").split(dt), L = (z || "").split(dt), te = (Kt(x, w) || "").split(dt).filter((Q) => !L.includes(Q)).concat(P).filter((Q, ie, K) => K.indexOf(Q) === ie).filter((Q) => Q).join(" ").trim();
          te ? An(x, w, te) : xn(x, w);
        }
      }
    }, T = computed(() => m(e, "content")), b = computed(() => B(e, "label")), k = computed(() => m(e, "label")), y = computed(
      () => Object.keys(T.value).length > 0 || Object.keys(k.value).length > 0
    ), C = computed(
      () => typeof n.value == "boolean" ? n.value : null
    ), q = computed(() => {
      const _ = C.value;
      return _ === true ? "is-valid" : _ === false ? "is-invalid" : null;
    }), E = computed(
      () => $t(t.ariaInvalid, n.value)
    );
    return watch(
      () => v,
      (_, z) => {
        _ !== z && V(_, z);
      }
    ), onMounted(() => {
      nextTick(() => {
        V(v);
      });
    }), {
      disabledBoolean: a,
      labelSrOnlyBoolean: l,
      stateBoolean: n,
      tooltipBoolean: u,
      validatedBoolean: f,
      floatingBoolean: c,
      ariaDescribedby: v,
      computedAriaInvalid: E,
      contentColProps: T,
      isHorizontal: y,
      labelAlignClasses: b,
      labelColProps: k,
      onLegendClick: (_) => {
        if (e.labelFor)
          return;
        const { target: z } = _, x = z ? z.tagName : "";
        if (zs.indexOf(x) !== -1)
          return;
        const w = _n(Ls, $).filter(Tn);
        w.length === 1 && Sn(w[0]);
      },
      stateClass: q
    };
  },
  render() {
    const e = this.$props, t = this.$slots, a = ke(), l = !e.labelFor;
    let n = null;
    const u = Fe(Ns, {}, t) || e.label, f = u ? Ee("_BV_label_") : null;
    if (u || this.isHorizontal) {
      const E = l ? "legend" : "label";
      if (this.labelSrOnlyBoolean)
        u && (n = h(
          E,
          {
            class: "visually-hidden",
            id: f,
            for: e.labelFor || null
          },
          u
        )), this.isHorizontal ? n = h(et, this.labelColProps, { default: () => n }) : n = h("div", {}, [n]);
      else {
        const I = {
          onClick: l ? this.onLegendClick : null,
          ...this.isHorizontal ? this.labelColProps : {},
          tag: this.isHorizontal ? E : null,
          id: f,
          for: e.labelFor || null,
          tabIndex: l ? "-1" : null,
          class: [
            this.isHorizontal ? "col-form-label" : "form-label",
            {
              "bv-no-focus-ring": l,
              "col-form-label": this.isHorizontal || l,
              "pt-0": !this.isHorizontal && l,
              "d-block": !this.isHorizontal && !l,
              [`col-form-label-${e.labelSize}`]: !!e.labelSize
            },
            this.labelAlignClasses,
            e.labelClass
          ]
        };
        this.isHorizontal ? n = h(et, I, { default: () => u }) : n = h(E, I, u);
      }
    }
    let c = null;
    const v = Fe(Es, {}, t) || this.invalidFeedback, p = v ? Ee("_BV_feedback_invalid_") : void 0;
    v && (c = h(
      Dt,
      {
        ariaLive: e.feedbackAriaLive,
        id: p,
        state: this.stateBoolean,
        tooltip: this.tooltipBoolean
      },
      { default: () => v }
    ));
    let B = null;
    const m = Fe(Ds, {}, t) || this.validFeedback, $ = m ? Ee("_BV_feedback_valid_") : void 0;
    m && (B = h(
      Rt,
      {
        ariaLive: e.feedbackAriaLive,
        id: $,
        state: this.stateBoolean,
        tooltip: this.tooltipBoolean
      },
      { default: () => m }
    ));
    let V = null;
    const T = Fe(Hs, {}, t) || this.description, b = T ? Ee("_BV_description_") : void 0;
    T && (V = h(
      Ht,
      {
        id: b
      },
      { default: () => T }
    ));
    const k = this.ariaDescribedby = [
      b,
      this.stateBoolean === false ? p : null,
      this.stateBoolean === true ? $ : null
    ].filter((E) => E).join(" ") || null, y = [
      Fe(Rs, { ariaDescribedby: k, descriptionId: b, id: a, labelId: f }, t) || "",
      c,
      B,
      V
    ];
    !this.isHorizontal && this.floatingBoolean && y.push(n);
    let C = h(
      "div",
      {
        ref: "content",
        class: [
          {
            "form-floating": !this.isHorizontal && this.floatingBoolean
          }
        ]
      },
      y
    );
    this.isHorizontal && (C = h(et, { ref: "content", ...this.contentColProps }, { default: () => y }));
    const q = {
      class: [
        "mb-3",
        this.stateClass,
        {
          "was-validated": this.validatedBoolean
        }
      ],
      id: ke(toRef(e, "id")).value,
      disabled: l ? this.disabledBoolean : null,
      role: l ? null : "group",
      "aria-invalid": this.computedAriaInvalid,
      "aria-labelledby": l && this.isHorizontal ? f : null
    };
    return this.isHorizontal && !l ? h(ft, q, { default: () => [n, C] }) : h(
      l ? "fieldset" : "div",
      q,
      this.isHorizontal && l ? [h(ft, {}, { default: () => [n, C] })] : this.isHorizontal || !this.floatingBoolean ? [n, C] : [C]
    );
  }
});
var ba = [
  "text",
  "number",
  "email",
  "password",
  "search",
  "url",
  "tel",
  "date",
  "time",
  "range",
  "color"
];
var Ms = defineComponent({
  props: {
    ...el,
    max: { type: [String, Number], required: false },
    min: { type: [String, Number], required: false },
    step: { type: [String, Number], required: false },
    type: {
      type: String,
      default: "text",
      validator: (e) => ba.includes(e)
    }
  },
  emits: ["update:modelValue", "change", "blur", "input"],
  setup(e, { emit: t }) {
    const { input: a, computedId: l, computedAriaInvalid: n, onInput: u, onChange: f, onBlur: c, focus: v, blur: p } = tl(e, t), B = ref(false), m = computed(() => {
      const T = e.type === "range", b = e.type === "color";
      return {
        "form-control-highlighted": B.value,
        "form-range": T,
        "form-control": b || !e.plaintext && !T,
        "form-control-color": b,
        "form-control-plaintext": e.plaintext && !T && !b,
        [`form-control-${e.size}`]: !!e.size,
        "is-valid": e.state === true,
        "is-invalid": e.state === false
      };
    }), $ = computed(
      () => ba.includes(e.type) ? e.type : "text"
    );
    return {
      computedClasses: m,
      localType: $,
      input: a,
      computedId: l,
      computedAriaInvalid: n,
      onInput: u,
      onChange: f,
      onBlur: c,
      focus: v,
      blur: p,
      highlight: () => {
        B.value !== true && (B.value = true, setTimeout(() => {
          B.value = false;
        }, 2e3));
      }
    };
  }
});
var js = ["id", "name", "form", "type", "disabled", "placeholder", "required", "autocomplete", "readonly", "min", "max", "step", "list", "aria-required", "aria-invalid"];
function Gs(e, t, a, l, n, u) {
  return openBlock(), createElementBlock("input", mergeProps({
    id: e.computedId,
    ref: "input",
    class: e.computedClasses,
    name: e.name || void 0,
    form: e.form || void 0,
    type: e.localType,
    disabled: e.disabled,
    placeholder: e.placeholder,
    required: e.required,
    autocomplete: e.autocomplete || void 0,
    readonly: e.readonly || e.plaintext,
    min: e.min,
    max: e.max,
    step: e.step,
    list: e.type !== "password" ? e.list : void 0,
    "aria-required": e.required ? "true" : void 0,
    "aria-invalid": e.computedAriaInvalid
  }, e.$attrs, {
    onInput: t[0] || (t[0] = (f) => e.onInput(f)),
    onChange: t[1] || (t[1] = (f) => e.onChange(f)),
    onBlur: t[2] || (t[2] = (f) => e.onBlur(f))
  }), null, 16, js);
}
var Us = _e(Ms, [["render", Gs]]);
var Ws = ["id", "disabled", "required", "name", "form", "aria-label", "aria-labelledby", "value", "aria-required"];
var Ks = ["for"];
var kl = defineComponent({
  __name: "BFormRadio",
  props: {
    ariaLabel: null,
    ariaLabelledby: null,
    form: null,
    id: null,
    name: null,
    size: null,
    autofocus: { default: false },
    modelValue: { type: [Boolean, String, Array, Object, Number], default: void 0 },
    plain: { default: false },
    button: { default: false },
    switch: { default: false },
    disabled: { default: false },
    buttonVariant: { default: "secondary" },
    inline: { default: false },
    required: { default: false },
    state: { default: void 0 },
    value: { type: [String, Boolean, Object, Number], default: true }
  },
  emits: ["input", "change", "update:modelValue"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = ke(toRef(a, "id"), "form-check"), u = r(toRef(a, "autofocus")), f = r(toRef(a, "plain")), c = r(toRef(a, "button")), v = r(toRef(a, "switch")), p = r(toRef(a, "disabled")), B = r(toRef(a, "inline")), m = r(toRef(a, "required")), $ = r(toRef(a, "state")), V = ref(null), T = ref(false), b = computed({
      get: () => Array.isArray(a.modelValue) ? a.modelValue[0] : a.modelValue,
      set: (_) => {
        const z = _ ? a.value : false, x = Array.isArray(a.modelValue) ? [z] : z;
        t("input", x), t("change", x), t("update:modelValue", x);
      }
    }), k = computed(() => Array.isArray(a.modelValue) ? (a.modelValue || []).find((_) => _ === a.value) : JSON.stringify(a.modelValue) === JSON.stringify(a.value)), y = computed(() => !Ve(l.default)), C = reactive({
      plain: toRef(f, "value"),
      button: toRef(c, "value"),
      inline: toRef(B, "value"),
      switch: toRef(v, "value"),
      size: toRef(a, "size"),
      state: toRef($, "value"),
      buttonVariant: toRef(a, "buttonVariant")
    }), q = Wa(C), E = Ka(C), I = Xa(C);
    return onMounted(() => {
      u.value && V.value !== null && V.value.focus();
    }), (_, z) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(unref(q))
    }, [
      withDirectives(createBaseVNode("input", mergeProps({ id: unref(n) }, _.$attrs, {
        ref_key: "input",
        ref: V,
        "onUpdate:modelValue": z[0] || (z[0] = (x) => isRef(b) ? b.value = x : null),
        class: unref(E),
        type: "radio",
        disabled: unref(p),
        required: !!e.name && unref(m),
        name: e.name,
        form: e.form,
        "aria-label": e.ariaLabel,
        "aria-labelledby": e.ariaLabelledby,
        value: e.value,
        "aria-required": !!e.name && unref(m) ? true : void 0,
        onFocus: z[1] || (z[1] = (x) => T.value = true),
        onBlur: z[2] || (z[2] = (x) => T.value = false)
      }), null, 16, Ws), [
        [vModelRadio, unref(b)]
      ]),
      unref(y) || unref(f) === false ? (openBlock(), createElementBlock("label", {
        key: 0,
        for: unref(n),
        class: normalizeClass([unref(I), { active: unref(k), focus: T.value }])
      }, [
        renderSlot(_.$slots, "default")
      ], 10, Ks)) : createCommentVNode("", true)
    ], 2));
  }
});
var Xs = ["id"];
var Js = ["innerHTML"];
var Ys = ["textContent"];
var Zs = defineComponent({
  __name: "BFormRadioGroup",
  props: {
    size: null,
    form: null,
    id: null,
    name: null,
    modelValue: { type: [String, Boolean, Array, Object, Number], default: "" },
    ariaInvalid: { default: void 0 },
    autofocus: { default: false },
    buttonVariant: { default: "secondary" },
    buttons: { default: false },
    disabled: { default: false },
    disabledField: { default: "disabled" },
    htmlField: { default: "html" },
    options: { default: () => [] },
    plain: { default: false },
    required: { default: false },
    stacked: { default: false },
    state: { default: void 0 },
    textField: { default: "text" },
    validated: { default: false },
    valueField: { default: "value" }
  },
  emits: ["input", "update:modelValue", "change"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = "BFormRadio", u = ke(toRef(a, "id"), "radio"), f = ke(toRef(a, "name"), "checkbox");
    r(toRef(a, "autofocus"));
    const c = r(toRef(a, "buttons")), v = r(toRef(a, "disabled"));
    r(toRef(a, "plain"));
    const p = r(toRef(a, "required")), B = r(toRef(a, "stacked")), m = r(toRef(a, "state")), $ = r(toRef(a, "validated")), V = computed({
      get: () => a.modelValue,
      set: (C) => {
        t("input", C), t("update:modelValue", C), t("change", C);
      }
    }), T = computed(
      () => (l.first ? gt(l.first(), n, v.value) : []).concat(a.options.map((C) => Za(C, a))).concat(l.default ? gt(l.default(), n, v.value) : []).map((C, q) => Qa(C, q, a, f, u)).map((C) => ({
        ...C
      }))
    ), b = reactive({
      required: toRef(p, "value"),
      ariaInvalid: toRef(a, "ariaInvalid"),
      state: toRef(m, "value"),
      validated: toRef($, "value"),
      buttons: toRef(c, "value"),
      stacked: toRef(B, "value"),
      size: toRef(a, "size")
    }), k = Ja(b), y = Ya(b);
    return (C, q) => (openBlock(), createElementBlock("div", mergeProps(unref(k), {
      id: unref(u),
      role: "radiogroup",
      class: [unref(y), "bv-no-focus-ring"],
      tabindex: "-1"
    }), [
      (openBlock(true), createElementBlock(Fragment, null, renderList(unref(T), (E, I) => (openBlock(), createBlock(kl, mergeProps({
        key: I,
        modelValue: unref(V),
        "onUpdate:modelValue": q[0] || (q[0] = (_) => isRef(V) ? V.value = _ : null)
      }, E.props), {
        default: withCtx(() => [
          E.html ? (openBlock(), createElementBlock("span", {
            key: 0,
            innerHTML: E.html
          }, null, 8, Js)) : (openBlock(), createElementBlock("span", {
            key: 1,
            textContent: toDisplayString(E.text)
          }, null, 8, Ys))
        ]),
        _: 2
      }, 1040, ["modelValue"]))), 128))
    ], 16, Xs));
  }
});
var Qs = ["value", "disabled"];
var Qt = defineComponent({
  __name: "BFormSelectOption",
  props: {
    value: null,
    disabled: { default: false }
  },
  setup(e) {
    const a = r(toRef(e, "disabled"));
    return (l, n) => (openBlock(), createElementBlock("option", {
      value: e.value,
      disabled: unref(a)
    }, [
      renderSlot(l.$slots, "default")
    ], 8, Qs));
  }
});
var ei = ["label"];
var Cl = defineComponent({
  __name: "BFormSelectOptionGroup",
  props: {
    label: null,
    disabledField: { default: "disabled" },
    htmlField: { default: "html" },
    options: { default: () => [] },
    textField: { default: "text" },
    valueField: { default: "value" }
  },
  setup(e) {
    const t = e, a = computed(
      () => Jt(t.options, "BFormSelectOptionGroup", t)
    );
    return (l, n) => (openBlock(), createElementBlock("optgroup", { label: e.label }, [
      renderSlot(l.$slots, "first"),
      (openBlock(true), createElementBlock(Fragment, null, renderList(unref(a), (u, f) => (openBlock(), createBlock(Qt, mergeProps({
        key: f,
        value: u.value,
        disabled: u.disabled
      }, l.$attrs, {
        innerHTML: u.html || u.text
      }), null, 16, ["value", "disabled", "innerHTML"]))), 128)),
      renderSlot(l.$slots, "default")
    ], 8, ei));
  }
});
var ti = ["id", "name", "form", "multiple", "size", "disabled", "required", "aria-required", "aria-invalid"];
var ai = defineComponent({
  __name: "BFormSelect",
  props: {
    ariaInvalid: { default: void 0 },
    autofocus: { default: false },
    disabled: { default: false },
    disabledField: { default: "disabled" },
    form: null,
    htmlField: { default: "html" },
    id: null,
    labelField: { default: "label" },
    multiple: { default: false },
    name: null,
    options: { default: () => [] },
    optionsField: { default: "options" },
    plain: { default: false },
    required: { default: false },
    selectSize: { default: 0 },
    size: null,
    state: { default: void 0 },
    textField: { default: "text" },
    valueField: { default: "value" },
    modelValue: { default: "" }
  },
  emits: ["input", "update:modelValue", "change"],
  setup(e, { expose: t, emit: a }) {
    const l = e, n = ke(toRef(l, "id"), "input"), u = r(toRef(l, "autofocus")), f = r(toRef(l, "disabled")), c = r(toRef(l, "multiple")), v = r(toRef(l, "plain")), p = r(toRef(l, "required")), B = r(toRef(l, "state")), m = ref(), $ = computed(() => ({
      "form-control": v.value,
      [`form-control-${l.size}`]: l.size && v.value,
      "form-select": !v.value,
      [`form-select-${l.size}`]: l.size && !v.value,
      "is-valid": B.value === true,
      "is-invalid": B.value === false
    })), V = computed(() => {
      if (l.selectSize || v.value)
        return l.selectSize;
    }), T = computed(
      () => $t(l.ariaInvalid, B.value)
    ), b = computed(
      () => Jt(l.options, "BFormSelect", l)
    ), k = computed({
      get() {
        return l.modelValue;
      },
      set(E) {
        a("change", E), a("update:modelValue", E), a("input", E);
      }
    }), y = () => {
      var E;
      f.value || (E = m.value) == null || E.focus();
    }, C = () => {
      var E;
      f.value || (E = m.value) == null || E.blur();
    }, q = () => {
      nextTick(() => {
        var E;
        u.value && ((E = m.value) == null || E.focus());
      });
    };
    return onMounted(q), onActivated(q), t({
      blur: C,
      focus: y
    }), (E, I) => withDirectives((openBlock(), createElementBlock("select", mergeProps({
      id: unref(n),
      ref_key: "input",
      ref: m
    }, E.$attrs, {
      "onUpdate:modelValue": I[0] || (I[0] = (_) => isRef(k) ? k.value = _ : null),
      class: unref($),
      name: e.name,
      form: e.form || void 0,
      multiple: unref(c) || void 0,
      size: unref(V),
      disabled: unref(f),
      required: unref(p),
      "aria-required": unref(p) ? true : void 0,
      "aria-invalid": unref(T)
    }), [
      renderSlot(E.$slots, "first"),
      (openBlock(true), createElementBlock(Fragment, null, renderList(unref(b), (_, z) => (openBlock(), createElementBlock(Fragment, { key: z }, [
        Array.isArray(_.options) ? (openBlock(), createBlock(Cl, {
          key: 0,
          label: _.label,
          options: _.options
        }, null, 8, ["label", "options"])) : (openBlock(), createBlock(Qt, {
          key: 1,
          value: _.value,
          disabled: _.disabled,
          innerHTML: _.html || _.text
        }, null, 8, ["value", "disabled", "innerHTML"]))
      ], 64))), 128)),
      renderSlot(E.$slots, "default")
    ], 16, ti)), [
      [vModelSelect, unref(k)]
    ]);
  }
});
var li = ["id"];
var Sl = defineComponent({
  __name: "BFormTag",
  props: {
    id: null,
    title: null,
    disabled: { default: false },
    noRemove: { default: false },
    pill: { default: false },
    removeLabel: { default: "Remove tag" },
    tag: { default: "span" },
    variant: { default: "secondary" }
  },
  emits: ["remove"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = ke(toRef(a, "id")), u = r(toRef(a, "disabled")), f = r(toRef(a, "noRemove")), c = r(toRef(a, "pill")), v = computed(
      () => {
        var m, $, V;
        return (V = (($ = (m = l.default) == null ? void 0 : m.call(l)[0].children) != null ? $ : "").toString() || a.title) != null ? V : "";
      }
    ), p = computed(() => `${n.value}taglabel__`), B = computed(() => [
      `bg-${a.variant}`,
      {
        "text-dark": ["warning", "info", "light"].includes(a.variant),
        "rounded-pill": c.value,
        disabled: u.value
      }
    ]);
    return (m, $) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: unref(n),
      title: unref(v),
      class: normalizeClass(["badge b-form-tag d-inline-flex align-items-center mw-100", unref(B)]),
      "aria-labelledby": unref(p)
    }, {
      default: withCtx(() => [
        createBaseVNode("span", {
          id: unref(p),
          class: "b-form-tag-content flex-grow-1 text-truncate"
        }, [
          renderSlot(m.$slots, "default", {}, () => [
            createTextVNode(toDisplayString(unref(v)), 1)
          ])
        ], 8, li),
        !unref(u) && !unref(f) ? (openBlock(), createBlock(Ze, {
          key: 0,
          "aria-keyshortcuts": "Delete",
          "aria-label": e.removeLabel,
          class: "b-form-tag-remove",
          white: !["warning", "info", "light"].includes(e.variant),
          "aria-describedby": unref(p),
          "aria-controls": e.id,
          onClick: $[0] || ($[0] = (V) => t("remove", unref(v)))
        }, null, 8, ["aria-label", "white", "aria-describedby", "aria-controls"])) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["id", "title", "class", "aria-labelledby"]));
  }
});
var ni = ["id"];
var oi = ["id", "for", "aria-live"];
var si = ["id", "aria-live"];
var ii = ["id"];
var ri = ["aria-controls"];
var ui = {
  role: "group",
  class: "d-flex"
};
var di = ["id", "disabled", "value", "type", "placeholder", "form", "required"];
var ci = ["disabled"];
var fi = {
  "aria-live": "polite",
  "aria-atomic": "true"
};
var vi = {
  key: 0,
  class: "d-block invalid-feedback"
};
var mi = {
  key: 1,
  class: "form-text text-muted"
};
var bi = {
  key: 2,
  class: "form-text text-muted"
};
var gi = ["name", "value"];
var pi = defineComponent({
  __name: "BFormTags",
  props: {
    addButtonText: { default: "Add" },
    addButtonVariant: { default: "outline-secondary" },
    addOnChange: { default: false },
    autofocus: { default: false },
    disabled: { default: false },
    duplicateTagText: { default: "Duplicate tag(s)" },
    inputAttrs: null,
    inputClass: null,
    inputId: null,
    inputType: { default: "text" },
    invalidTagText: { default: "Invalid tag(s)" },
    form: null,
    limit: null,
    limitTagsText: { default: "Tag limit reached" },
    modelValue: { default: () => [] },
    name: null,
    noAddOnEnter: { default: false },
    noOuterFocus: { default: false },
    noTagRemove: { default: false },
    placeholder: { default: "Add tag..." },
    removeOnDelete: { default: false },
    required: { default: false },
    separator: null,
    state: { default: void 0 },
    size: null,
    tagClass: null,
    tagPills: { default: false },
    tagRemoveLabel: null,
    tagRemovedLabel: { default: "Tag removed" },
    tagValidator: { type: Function, default: () => true },
    tagVariant: { default: "secondary" }
  },
  emits: ["update:modelValue", "input", "tag-state", "focus", "focusin", "focusout", "blur"],
  setup(e, { emit: t }) {
    const a = e, l = ke(), n = r(toRef(a, "addOnChange")), u = r(toRef(a, "autofocus")), f = r(toRef(a, "disabled")), c = r(toRef(a, "noAddOnEnter")), v = r(toRef(a, "noOuterFocus")), p = r(toRef(a, "noTagRemove")), B = r(toRef(a, "removeOnDelete")), m = r(toRef(a, "required")), $ = r(toRef(a, "state")), V = r(toRef(a, "tagPills")), T = ref(null), b = computed(() => a.inputId || `${l.value}input__`), k = ref(a.modelValue), y = ref(""), C = ref(false), q = ref(false), E = ref(""), I = ref([]), _ = ref([]), z = ref([]), x = computed(() => ({
      [`form-control-${a.size}`]: a.size !== void 0,
      disabled: f.value,
      focus: q.value,
      "is-invalid": $.value === false,
      "is-valid": $.value === true
    })), w = computed(() => k.value.includes(y.value)), P = computed(
      () => y.value === "" ? false : !a.tagValidator(y.value)
    ), L = computed(() => k.value.length === a.limit), te = computed(() => !P.value && !w.value), Q = computed(() => ({
      addButtonText: a.addButtonText,
      addButtonVariant: a.addButtonVariant,
      addTag: re,
      disableAddButton: te.value,
      disabled: f.value,
      duplicateTagText: a.duplicateTagText,
      duplicateTags: z.value,
      form: a.form,
      inputAttrs: {
        ...a.inputAttrs,
        disabled: f.value,
        form: a.form,
        id: b,
        value: y
      },
      inputHandlers: {
        input: ge,
        keydown: pe,
        change: ye
      },
      inputId: b,
      inputType: a.inputType,
      invalidTagText: a.invalidTagText,
      invalidTags: _.value,
      isDuplicate: w.value,
      isInvalid: P.value,
      isLimitReached: L.value,
      limitTagsText: a.limitTagsText,
      limit: a.limit,
      noTagRemove: p.value,
      placeholder: a.placeholder,
      removeTag: H,
      required: m.value,
      separator: a.separator,
      size: a.size,
      state: $.value,
      tagClass: a.tagClass,
      tagPills: V.value,
      tagRemoveLabel: a.tagRemoveLabel,
      tagVariant: a.tagVariant,
      tags: k.value
    }));
    watch(
      () => a.modelValue,
      (N) => {
        k.value = N;
      }
    );
    const ie = () => {
      var N;
      u.value && ((N = T.value) == null || N.focus());
    }, K = (N) => {
      if (f.value) {
        N.target.blur();
        return;
      }
      t("focusin", N);
    }, me = (N) => {
      f.value || v.value || (q.value = true, t("focus", N));
    }, ae = (N) => {
      q.value = false, t("blur", N);
    }, ge = (N) => {
      var ne, Y;
      const X = typeof N == "string" ? N : N.target.value;
      if (C.value = false, ((ne = a.separator) == null ? void 0 : ne.includes(X.charAt(0))) && X.length > 0) {
        T.value && (T.value.value = "");
        return;
      }
      if (y.value = X, (Y = a.separator) != null && Y.includes(X.charAt(X.length - 1))) {
        re(X.slice(0, X.length - 1));
        return;
      }
      I.value = a.tagValidator(X) && !w.value ? [X] : [], _.value = a.tagValidator(X) ? [] : [X], z.value = w.value ? [X] : [], t("tag-state", I.value, _.value, z.value);
    }, ye = (N) => {
      n.value && (ge(N), w.value || re(y.value));
    }, pe = (N) => {
      if (N.key === "Enter" && !c.value) {
        re(y.value);
        return;
      }
      (N.key === "Backspace" || N.key === "Delete") && B.value && y.value === "" && C.value && k.value.length > 0 ? H(k.value[k.value.length - 1]) : C.value = true;
    }, re = (N) => {
      var ne;
      if (N = (N || y.value).trim(), N === "" || w.value || !a.tagValidator(N) || a.limit && L.value)
        return;
      const X = [...a.modelValue, N];
      y.value = "", C.value = true, t("update:modelValue", X), t("input", X), (ne = T.value) == null || ne.focus();
    }, H = (N) => {
      var ne;
      const X = k.value.indexOf((ne = N == null ? void 0 : N.toString()) != null ? ne : "");
      E.value = k.value.splice(X, 1).toString(), t("update:modelValue", k.value);
    };
    return onMounted(() => {
      ie(), a.modelValue.length > 0 && (C.value = true);
    }), onActivated(() => ie()), (N, X) => (openBlock(), createElementBlock("div", {
      id: unref(l),
      class: normalizeClass(["b-form-tags form-control h-auto", unref(x)]),
      role: "group",
      tabindex: "-1",
      onFocusin: K,
      onFocusout: X[1] || (X[1] = (ne) => t("focusout", ne))
    }, [
      createBaseVNode("output", {
        id: `${unref(l)}selected_tags__`,
        class: "visually-hidden",
        role: "status",
        for: unref(b),
        "aria-live": q.value ? "polite" : "off",
        "aria-atomic": "true",
        "aria-relevant": "additions text"
      }, toDisplayString(k.value.join(", ")), 9, oi),
      createBaseVNode("div", {
        id: `${unref(l)}removed_tags__`,
        role: "status",
        "aria-live": q.value ? "assertive" : "off",
        "aria-atomic": "true",
        class: "visually-hidden"
      }, " (" + toDisplayString(e.tagRemovedLabel) + ") " + toDisplayString(E.value), 9, si),
      renderSlot(N.$slots, "default", normalizeProps(guardReactiveProps(unref(Q))), () => [
        createBaseVNode("ul", {
          id: `${unref(l)}tag_list__`,
          class: "b-form-tags-list list-unstyled mb-0 d-flex flex-wrap align-items-center"
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(k.value, (ne, Y) => renderSlot(N.$slots, "tag", normalizeProps(mergeProps({ key: Y }, { tag: ne, tagClass: e.tagClass, tagVariant: e.tagVariant, tagPills: unref(V), removeTag: H })), () => [
            createVNode(Sl, {
              class: normalizeClass(e.tagClass),
              tag: "li",
              variant: e.tagVariant,
              pill: e.tagPills,
              onRemove: H
            }, {
              default: withCtx(() => [
                createTextVNode(toDisplayString(ne), 1)
              ]),
              _: 2
            }, 1032, ["class", "variant", "pill"])
          ])), 128)),
          createBaseVNode("li", {
            role: "none",
            "aria-live": "off",
            class: "b-from-tags-field flex-grow-1",
            "aria-controls": `${unref(l)}tag_list__`
          }, [
            createBaseVNode("div", ui, [
              createBaseVNode("input", mergeProps({
                id: unref(b),
                ref_key: "input",
                ref: T,
                disabled: unref(f),
                value: y.value,
                type: e.inputType,
                placeholder: e.placeholder,
                class: "b-form-tags-input w-100 flex-grow-1 p-0 m-0 bg-transparent border-0",
                style: { outline: "currentcolor none 0px", "min-width": "5rem" }
              }, e.inputAttrs, {
                form: e.form,
                required: unref(m),
                onInput: ge,
                onChange: ye,
                onKeydown: pe,
                onFocus: me,
                onBlur: ae
              }), null, 16, di),
              unref(te) ? (openBlock(), createElementBlock("button", {
                key: 0,
                type: "button",
                class: normalizeClass(["btn b-form-tags-button py-0", [
                  `btn-${e.addButtonVariant}`,
                  {
                    "disabled invisible": y.value.length === 0
                  },
                  e.inputClass
                ]]),
                style: { "font-size": "90%" },
                disabled: unref(f) || y.value.length === 0 || unref(L),
                onClick: X[0] || (X[0] = (ne) => re(y.value))
              }, [
                renderSlot(N.$slots, "add-button-text", {}, () => [
                  createTextVNode(toDisplayString(e.addButtonText), 1)
                ])
              ], 10, ci)) : createCommentVNode("", true)
            ])
          ], 8, ri)
        ], 8, ii),
        createBaseVNode("div", fi, [
          unref(P) ? (openBlock(), createElementBlock("div", vi, toDisplayString(e.invalidTagText) + ": " + toDisplayString(y.value), 1)) : createCommentVNode("", true),
          unref(w) ? (openBlock(), createElementBlock("small", mi, toDisplayString(e.duplicateTagText) + ": " + toDisplayString(y.value), 1)) : createCommentVNode("", true),
          k.value.length === e.limit ? (openBlock(), createElementBlock("small", bi, "Tag limit reached")) : createCommentVNode("", true)
        ])
      ]),
      e.name ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(k.value, (ne, Y) => (openBlock(), createElementBlock("input", {
        key: Y,
        type: "hidden",
        name: e.name,
        value: ne
      }, null, 8, gi))), 128)) : createCommentVNode("", true)
    ], 42, ni));
  }
});
var hi = defineComponent({
  props: {
    ...el,
    noResize: { type: [Boolean, String], default: false },
    rows: { type: [String, Number], required: false, default: 2 },
    wrap: { type: String, default: "soft" }
  },
  emits: ["update:modelValue", "change", "blur", "input"],
  setup(e, { emit: t }) {
    const { input: a, computedId: l, computedAriaInvalid: n, onInput: u, onChange: f, onBlur: c, focus: v, blur: p } = tl(e, t), B = r(toRef(e, "noResize")), m = computed(() => ({
      "form-control": !e.plaintext,
      "form-control-plaintext": e.plaintext,
      [`form-control-${e.size}`]: !!e.size,
      "is-valid": e.state === true,
      "is-invalid": e.state === false
    })), $ = computed(
      () => B.value ? { resize: "none" } : void 0
    );
    return {
      input: a,
      computedId: l,
      computedAriaInvalid: n,
      onInput: u,
      onChange: f,
      onBlur: c,
      focus: v,
      blur: p,
      computedClasses: m,
      computedStyles: $
    };
  }
});
var yi = ["id", "name", "form", "disabled", "placeholder", "required", "autocomplete", "readonly", "aria-required", "aria-invalid", "rows", "wrap"];
function Bi(e, t, a, l, n, u) {
  return openBlock(), createElementBlock("textarea", mergeProps({
    id: e.computedId,
    ref: "input",
    class: e.computedClasses,
    name: e.name || void 0,
    form: e.form || void 0,
    disabled: e.disabled,
    placeholder: e.placeholder,
    required: e.required,
    autocomplete: e.autocomplete || void 0,
    readonly: e.readonly || e.plaintext,
    "aria-required": e.required ? "true" : void 0,
    "aria-invalid": e.computedAriaInvalid,
    rows: e.rows,
    style: e.computedStyles,
    wrap: e.wrap || void 0
  }, e.$attrs, {
    onInput: t[0] || (t[0] = (f) => e.onInput(f)),
    onChange: t[1] || (t[1] = (f) => e.onChange(f)),
    onBlur: t[2] || (t[2] = (f) => e.onBlur(f))
  }), null, 16, yi);
}
var $i = _e(hi, [["render", Bi]]);
var ki = {
  key: 0,
  class: "input-group-text"
};
var Ci = ["innerHTML"];
var Si = { key: 1 };
var wi = {
  key: 0,
  class: "input-group-text"
};
var Ti = ["innerHTML"];
var _i = { key: 1 };
var Vi = defineComponent({
  __name: "BInputGroup",
  props: {
    append: null,
    appendHtml: null,
    id: null,
    prepend: null,
    prependHtml: null,
    size: null,
    tag: { default: "div" }
  },
  setup(e) {
    const t = e, a = computed(() => ({
      "input-group-sm": t.size === "sm",
      "input-group-lg": t.size === "lg"
    })), l = computed(() => !!t.append || !!t.appendHtml), n = computed(() => !!t.prepend || !!t.prependHtml);
    return (u, f) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: e.id,
      class: normalizeClass(["input-group", unref(a)]),
      role: "group"
    }, {
      default: withCtx(() => [
        renderSlot(u.$slots, "prepend", {}, () => [
          unref(n) ? (openBlock(), createElementBlock("span", ki, [
            e.prependHtml ? (openBlock(), createElementBlock("span", {
              key: 0,
              innerHTML: e.prependHtml
            }, null, 8, Ci)) : (openBlock(), createElementBlock("span", Si, toDisplayString(e.prepend), 1))
          ])) : createCommentVNode("", true)
        ]),
        renderSlot(u.$slots, "default"),
        renderSlot(u.$slots, "append", {}, () => [
          unref(l) ? (openBlock(), createElementBlock("span", wi, [
            e.appendHtml ? (openBlock(), createElementBlock("span", {
              key: 0,
              innerHTML: e.appendHtml
            }, null, 8, Ti)) : (openBlock(), createElementBlock("span", _i, toDisplayString(e.append), 1))
          ])) : createCommentVNode("", true)
        ])
      ]),
      _: 3
    }, 8, ["id", "class"]));
  }
});
var wl = defineComponent({
  __name: "BInputGroupText",
  props: {
    tag: { default: "div" },
    text: null
  },
  setup(e) {
    return (t, a) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), { class: "input-group-text" }, {
      default: withCtx(() => [
        renderSlot(t.$slots, "default", {}, () => [
          createTextVNode(toDisplayString(e.text), 1)
        ])
      ]),
      _: 3
    }));
  }
});
var ea = defineComponent({
  __name: "BInputGroupAddon",
  props: {
    isText: { default: false }
  },
  setup(e) {
    const a = r(toRef(e, "isText"));
    return (l, n) => unref(a) ? (openBlock(), createBlock(wl, { key: 0 }, {
      default: withCtx(() => [
        renderSlot(l.$slots, "default")
      ]),
      _: 3
    })) : renderSlot(l.$slots, "default", { key: 1 });
  }
});
var Ai = defineComponent({
  __name: "BInputGroupAppend",
  props: {
    isText: { default: false }
  },
  setup(e) {
    return (t, a) => (openBlock(), createBlock(ea, { "is-text": e.isText }, {
      default: withCtx(() => [
        renderSlot(t.$slots, "default")
      ]),
      _: 3
    }, 8, ["is-text"]));
  }
});
var xi = defineComponent({
  __name: "BInputGroupPrepend",
  props: {
    isText: { default: false }
  },
  setup(e) {
    return (t, a) => (openBlock(), createBlock(ea, { "is-text": e.isText }, {
      default: withCtx(() => [
        renderSlot(t.$slots, "default")
      ]),
      _: 3
    }, 8, ["is-text"]));
  }
});
var Tl = Symbol();
var Ii = defineComponent({
  __name: "BListGroup",
  props: {
    flush: { default: false },
    horizontal: { type: [Boolean, String], default: false },
    numbered: { default: false },
    tag: { default: "div" }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "flush")), l = r(toRef(t, "numbered")), n = computed(() => {
      const f = a.value ? false : t.horizontal;
      return {
        "list-group-flush": a.value,
        "list-group-horizontal": f === true,
        [`list-group-horizontal-${f}`]: typeof f == "string",
        "list-group-numbered": l.value
      };
    }), u = computed(() => l.value === true ? "ol" : t.tag);
    return provide(Tl, {
      numbered: l.value
    }), (f, c) => (openBlock(), createBlock(resolveDynamicComponent(unref(u)), {
      class: normalizeClass(["list-group", unref(n)])
    }, {
      default: withCtx(() => [
        renderSlot(f.$slots, "default")
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var Fi = defineComponent({
  __name: "BListGroupItem",
  props: {
    action: { default: false },
    active: { default: false },
    button: { default: false },
    disabled: { default: false },
    href: null,
    tag: { default: "div" },
    target: { default: "_self" },
    to: null,
    variant: null
  },
  setup(e) {
    const t = e, a = useAttrs(), l = inject(Tl, null), n = r(toRef(t, "action")), u = r(toRef(t, "active")), f = r(toRef(t, "button")), c = r(toRef(t, "disabled")), v = computed(() => !f.value && (!!t.href || !!t.to)), p = computed(
      () => l != null && l.numbered ? "li" : f.value ? "button" : v.value ? Ae : t.tag
    ), B = computed(
      () => n.value || v.value || f.value || ["a", "router-link", "button", "b-link"].includes(t.tag)
    ), m = computed(() => ({
      [`list-group-item-${t.variant}`]: t.variant !== void 0,
      "list-group-item-action": B.value,
      active: u.value,
      disabled: c.value
    })), $ = computed(() => {
      const V = {};
      return f.value && ((!a || !a.type) && (V.type = "button"), c.value && (V.disabled = true)), V;
    });
    return (V, T) => (openBlock(), createBlock(resolveDynamicComponent(unref(p)), mergeProps({
      class: ["list-group-item", unref(m)],
      "aria-current": unref(u) ? true : void 0,
      "aria-disabled": unref(c) ? true : void 0,
      target: unref(v) ? e.target : void 0,
      href: unref(f) ? void 0 : e.href,
      to: unref(f) ? void 0 : e.to
    }, unref($)), {
      default: withCtx(() => [
        renderSlot(V.$slots, "default")
      ]),
      _: 3
    }, 16, ["class", "aria-current", "aria-disabled", "target", "href", "to"]));
  }
});
var Pi = ["id", "aria-labelledby", "aria-describedby"];
var Oi = ["id"];
var Li = {
  inheritAttrs: false
};
var zi = defineComponent({
  ...Li,
  __name: "BModal",
  props: {
    bodyBgVariant: null,
    bodyClass: null,
    bodyTextVariant: null,
    busy: { default: false },
    lazy: { default: false },
    buttonSize: { default: "md" },
    cancelDisabled: { default: false },
    cancelTitle: { default: "Cancel" },
    cancelVariant: { default: "secondary" },
    centered: { default: false },
    contentClass: null,
    dialogClass: null,
    footerBgVariant: null,
    footerBorderVariant: null,
    footerClass: null,
    footerTextVariant: null,
    fullscreen: { type: [Boolean, String], default: false },
    headerBgVariant: null,
    headerBorderVariant: null,
    headerClass: null,
    headerCloseLabel: { default: "Close" },
    headerCloseWhite: { default: false },
    headerTextVariant: null,
    hideBackdrop: { default: false },
    hideFooter: { default: false },
    hideHeader: { default: false },
    hideHeaderClose: { default: false },
    id: null,
    modalClass: null,
    modelValue: { default: false },
    noCloseOnBackdrop: { default: false },
    noCloseOnEsc: { default: false },
    noFade: { default: false },
    noFocus: { default: false },
    okDisabled: { default: false },
    okOnly: { default: false },
    okTitle: { default: "Ok" },
    okVariant: { default: "primary" },
    scrollable: { default: false },
    show: { default: false },
    size: null,
    title: null,
    titleClass: null,
    titleSrOnly: { default: false },
    titleTag: { default: "h5" },
    static: { default: false }
  },
  emits: ["update:modelValue", "show", "shown", "hide", "hidden", "hide-prevented", "show-prevented", "ok", "cancel", "close"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = ke(toRef(a, "id"), "modal"), u = r(toRef(a, "busy")), f = r(toRef(a, "lazy")), c = r(toRef(a, "cancelDisabled")), v = r(toRef(a, "centered")), p = r(toRef(a, "hideBackdrop")), B = r(toRef(a, "hideFooter")), m = r(toRef(a, "hideHeader")), $ = r(toRef(a, "hideHeaderClose")), V = r(toRef(a, "modelValue")), T = r(toRef(a, "noCloseOnBackdrop")), b = r(toRef(a, "noCloseOnEsc")), k = r(toRef(a, "noFade")), y = r(toRef(a, "noFocus")), C = r(toRef(a, "okDisabled")), q = r(toRef(a, "okOnly")), E = r(toRef(a, "scrollable")), I = r(toRef(a, "titleSrOnly")), _ = r(toRef(a, "static")), z = ref(false), x = ref(null), w = ref(false), P = computed(() => [
      a.modalClass,
      {
        fade: !k.value,
        show: z.value
      }
    ]), L = computed(() => !Ve(l["header-close"])), te = computed(() => [
      a.dialogClass,
      {
        "modal-fullscreen": a.fullscreen === true,
        [`modal-fullscreen-${a.fullscreen}-down`]: typeof a.fullscreen == "string",
        [`modal-${a.size}`]: a.size !== void 0,
        "modal-dialog-centered": v.value,
        "modal-dialog-scrollable": E.value
      }
    ]), Q = computed(() => [
      a.bodyClass,
      {
        [`bg-${a.bodyBgVariant}`]: a.bodyBgVariant !== void 0,
        [`text-${a.bodyTextVariant}`]: a.bodyTextVariant !== void 0
      }
    ]), ie = computed(() => [
      a.headerClass,
      {
        [`bg-${a.headerBgVariant}`]: a.headerBgVariant !== void 0,
        [`border-${a.headerBorderVariant}`]: a.headerBorderVariant !== void 0,
        [`text-${a.headerTextVariant}`]: a.headerTextVariant !== void 0
      }
    ]), K = computed(() => [
      a.footerClass,
      {
        [`bg-${a.footerBgVariant}`]: a.footerBgVariant !== void 0,
        [`border-${a.footerBorderVariant}`]: a.footerBorderVariant !== void 0,
        [`text-${a.footerTextVariant}`]: a.footerTextVariant !== void 0
      }
    ]), me = computed(() => [
      a.titleClass,
      {
        ["visually-hidden"]: I.value
      }
    ]), ae = computed(() => c.value || u.value), ge = computed(() => C.value || u.value), ye = (Y, se = {}) => new sn(Y, {
      cancelable: false,
      target: x.value || null,
      relatedTarget: null,
      trigger: null,
      ...se,
      componentId: n.value
    }), pe = (Y = "") => {
      const se = ye("hide", { cancelable: Y !== "", trigger: Y });
      if (Y === "ok" && t(Y, se), Y === "cancel" && t(Y, se), Y === "close" && t(Y, se), t("hide", se), se.defaultPrevented || Y === "backdrop" && T.value || Y === "esc" && b.value) {
        t("update:modelValue", true), t("hide-prevented");
        return;
      }
      t("update:modelValue", false);
    }, re = () => {
      const Y = ye("show", { cancelable: true });
      if (t("show", Y), Y.defaultPrevented) {
        t("update:modelValue", false), t("show-prevented");
        return;
      }
      t("update:modelValue", true);
    }, H = () => re(), N = () => {
      z.value = true, t("shown", ye("shown")), f.value === true && (w.value = true);
    }, X = () => {
      z.value = false;
    }, ne = () => {
      t("hidden", ye("hidden")), f.value === true && (w.value = false);
    };
    return watch(
      () => V.value,
      (Y) => {
        Y === true && !y.value && nextTick(() => {
          x.value !== null && x.value.focus();
        });
      }
    ), (Y, se) => (openBlock(), createBlock(Teleport, {
      to: "body",
      disabled: unref(_)
    }, [
      createVNode(St, {
        "no-fade": true,
        "trans-props": { enterToClass: "show" },
        onBeforeEnter: H,
        onAfterEnter: N,
        onLeave: X,
        onAfterLeave: ne
      }, {
        default: withCtx(() => [
          withDirectives(createBaseVNode("div", mergeProps({
            id: unref(n),
            ref_key: "element",
            ref: x,
            class: ["modal", unref(P)],
            role: "dialog",
            "aria-labelledby": `${unref(n)}-label`,
            "aria-describedby": `${unref(n)}-body`,
            tabindex: "-1"
          }, Y.$attrs, {
            onKeyup: se[5] || (se[5] = withKeys((Te) => pe("esc"), ["esc"]))
          }), [
            createBaseVNode("div", {
              class: normalizeClass(["modal-dialog", unref(te)])
            }, [
              !unref(f) || unref(f) && w.value || unref(f) && unref(V) === true ? (openBlock(), createElementBlock("div", {
                key: 0,
                class: normalizeClass(["modal-content", e.contentClass])
              }, [
                unref(m) ? createCommentVNode("", true) : (openBlock(), createElementBlock("div", {
                  key: 0,
                  class: normalizeClass(["modal-header", unref(ie)])
                }, [
                  renderSlot(Y.$slots, "header", {}, () => [
                    (openBlock(), createBlock(resolveDynamicComponent(e.titleTag), {
                      id: `${unref(n)}-label`,
                      class: normalizeClass(["modal-title", unref(me)])
                    }, {
                      default: withCtx(() => [
                        renderSlot(Y.$slots, "title", {}, () => [
                          createTextVNode(toDisplayString(e.title), 1)
                        ], true)
                      ]),
                      _: 3
                    }, 8, ["id", "class"])),
                    unref($) ? createCommentVNode("", true) : (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                      unref(L) ? (openBlock(), createElementBlock("button", {
                        key: 0,
                        type: "button",
                        onClick: se[0] || (se[0] = (Te) => pe("close"))
                      }, [
                        renderSlot(Y.$slots, "header-close", {}, void 0, true)
                      ])) : (openBlock(), createBlock(Ze, {
                        key: 1,
                        "aria-label": e.headerCloseLabel,
                        white: e.headerCloseWhite,
                        onClick: se[1] || (se[1] = (Te) => pe("close"))
                      }, null, 8, ["aria-label", "white"]))
                    ], 64))
                  ], true)
                ], 2)),
                createBaseVNode("div", {
                  id: `${unref(n)}-body`,
                  class: normalizeClass(["modal-body", unref(Q)])
                }, [
                  renderSlot(Y.$slots, "default", {}, void 0, true)
                ], 10, Oi),
                unref(B) ? createCommentVNode("", true) : (openBlock(), createElementBlock("div", {
                  key: 1,
                  class: normalizeClass(["modal-footer", unref(K)])
                }, [
                  renderSlot(Y.$slots, "footer", {}, () => [
                    renderSlot(Y.$slots, "cancel", {}, () => [
                      unref(q) ? createCommentVNode("", true) : (openBlock(), createBlock(lt, {
                        key: 0,
                        type: "button",
                        class: "btn",
                        disabled: unref(ae),
                        size: e.buttonSize,
                        variant: e.cancelVariant,
                        onClick: se[2] || (se[2] = (Te) => pe("cancel"))
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(e.cancelTitle), 1)
                        ]),
                        _: 1
                      }, 8, ["disabled", "size", "variant"]))
                    ], true),
                    renderSlot(Y.$slots, "ok", {}, () => [
                      createVNode(lt, {
                        type: "button",
                        class: "btn",
                        disabled: unref(ge),
                        size: e.buttonSize,
                        variant: e.okVariant,
                        onClick: se[3] || (se[3] = (Te) => pe("ok"))
                      }, {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(e.okTitle), 1)
                        ]),
                        _: 1
                      }, 8, ["disabled", "size", "variant"])
                    ], true)
                  ], true)
                ], 2))
              ], 2)) : createCommentVNode("", true)
            ], 2),
            unref(p) ? createCommentVNode("", true) : renderSlot(Y.$slots, "backdrop", { key: 0 }, () => [
              createBaseVNode("div", {
                class: "modal-backdrop fade show",
                onClick: se[4] || (se[4] = (Te) => pe("backdrop"))
              })
            ], true)
          ], 16, Pi), [
            [vShow, unref(V)]
          ])
        ]),
        _: 3
      })
    ], 8, ["disabled"]));
  }
});
var Ni = _e(zi, [["__scopeId", "data-v-05140552"]]);
var Ei = defineComponent({
  __name: "BNav",
  props: {
    align: null,
    cardHeader: { default: false },
    fill: { default: false },
    justified: { default: false },
    pills: { default: false },
    small: { default: false },
    tabs: { default: false },
    tag: { default: "ul" },
    vertical: { default: false }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "cardHeader")), l = r(toRef(t, "fill")), n = r(toRef(t, "justified")), u = r(toRef(t, "pills")), f = r(toRef(t, "small")), c = r(toRef(t, "tabs")), v = r(toRef(t, "vertical")), p = ot(toRef(t, "align")), B = computed(() => ({
      "nav-tabs": c.value,
      "nav-pills": u.value && !c.value,
      "card-header-tabs": !v.value && a.value && c.value,
      "card-header-pills": !v.value && a.value && u.value && !c.value,
      "flex-column": v.value,
      "nav-fill": !v.value && l.value,
      "nav-justified": !v.value && n.value,
      [p.value]: !v.value && t.align !== void 0,
      small: f.value
    }));
    return (m, $) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(["nav", unref(B)])
    }, {
      default: withCtx(() => [
        renderSlot(m.$slots, "default")
      ]),
      _: 3
    }, 8, ["class"]));
  }
});
var Di = defineComponent({
  __name: "BNavForm",
  props: {
    role: null,
    id: null,
    floating: { default: false },
    novalidate: { default: false },
    validated: { default: false }
  },
  emits: ["submit"],
  setup(e, { emit: t }) {
    const a = e, l = computed(() => ({
      floating: a.floating,
      role: a.role,
      id: a.id,
      novalidate: a.novalidate,
      validated: a.validated
    })), n = (u) => t("submit", u);
    return (u, f) => (openBlock(), createBlock(yl, mergeProps(unref(l), {
      class: "d-flex",
      onSubmit: withModifiers(n, ["prevent"])
    }), {
      default: withCtx(() => [
        renderSlot(u.$slots, "default")
      ]),
      _: 3
    }, 16, ["onSubmit"]));
  }
});
var Hi = defineComponent({
  components: { BLink: Ae },
  props: {
    ...kt(Ge, ["event", "routerTag"])
  },
  setup(e) {
    return { disabledBoolean: r(toRef(e, "disabled")) };
  }
});
var Ri = { class: "nav-item" };
function qi(e, t, a, l, n, u) {
  const f = resolveComponent("b-link");
  return openBlock(), createElementBlock("li", Ri, [
    createVNode(f, mergeProps({ class: "nav-link" }, e.$props, {
      "active-class": "active",
      tabindex: e.disabledBoolean ? -1 : void 0,
      "aria-disabled": e.disabledBoolean ? true : void 0
    }), {
      default: withCtx(() => [
        renderSlot(e.$slots, "default")
      ]),
      _: 3
    }, 16, ["tabindex", "aria-disabled"])
  ]);
}
var Mi = _e(Hi, [["render", qi]]);
var ji = { class: "nav-item dropdown" };
var Gi = defineComponent({
  __name: "BNavItemDropdown",
  props: {
    id: null,
    text: null,
    toggleClass: null,
    size: null,
    offset: null,
    autoClose: { type: [Boolean, String], default: true },
    dark: { type: Boolean, default: false },
    dropleft: { type: Boolean, default: false },
    dropright: { type: Boolean, default: false },
    dropup: { type: Boolean, default: false },
    right: { type: Boolean, default: false },
    left: { type: [Boolean, String], default: false },
    split: { type: Boolean, default: false },
    splitVariant: null,
    noCaret: { type: Boolean, default: false },
    variant: { default: "link" }
  },
  setup(e) {
    const t = e;
    return (a, l) => (openBlock(), createElementBlock("li", ji, [
      createVNode(hl, mergeProps(t, { "is-nav": "" }), createSlots({ _: 2 }, [
        renderList(a.$slots, (n, u, f) => ({
          name: u,
          fn: withCtx((c) => [
            renderSlot(a.$slots, u, normalizeProps(guardReactiveProps(c || {})))
          ])
        }))
      ]), 1040)
    ]));
  }
});
var Ui = { class: "navbar-text" };
var Wi = defineComponent({
  __name: "BNavText",
  props: {
    text: null
  },
  setup(e) {
    return (t, a) => (openBlock(), createElementBlock("li", Ui, [
      renderSlot(t.$slots, "default", {}, () => [
        createTextVNode(toDisplayString(e.text), 1)
      ])
    ]));
  }
});
var Ki = defineComponent({
  __name: "BNavbar",
  props: {
    fixed: null,
    print: { default: false },
    sticky: null,
    tag: { default: "nav" },
    toggleable: { type: [Boolean, String], default: false },
    dark: { default: false },
    variant: null,
    container: { type: [String, Boolean], default: "fluid" }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "print")), l = r(toRef(t, "dark")), n = computed(
      () => t.tag === "nav" ? void 0 : "navigation"
    ), u = computed(
      () => typeof t.toggleable == "string" ? `navbar-expand-${t.toggleable}` : t.toggleable === false ? "navbar-expand" : void 0
    ), f = computed(
      () => t.container === true ? "container" : "container-fluid"
    ), c = computed(() => ({
      "d-print": a.value,
      [`sticky-${t.sticky}`]: t.sticky !== void 0,
      "navbar-dark": l.value,
      [`bg-${t.variant}`]: t.variant !== void 0,
      [`fixed-${t.fixed}`]: t.fixed !== void 0,
      [`${u.value}`]: u.value !== void 0
    }));
    return (v, p) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(["navbar", unref(c)]),
      role: unref(n)
    }, {
      default: withCtx(() => [
        e.container !== false ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(unref(f))
        }, [
          renderSlot(v.$slots, "default")
        ], 2)) : renderSlot(v.$slots, "default", { key: 1 })
      ]),
      _: 3
    }, 8, ["class", "role"]));
  }
});
var ga = kt(Ge, ["event", "routerTag"]);
var Xi = defineComponent({
  components: {
    BLink: Ae
  },
  props: {
    tag: { type: String, default: "div" },
    ...ga
  },
  setup(e) {
    const t = computed(() => at(e)), a = computed(
      () => t.value ? Ae : e.tag
    );
    return {
      computedLinkProps: computed(
        () => t.value ? Xt(e, ga) : {}
      ),
      computedTag: a
    };
  }
});
function Ji(e, t, a, l, n, u) {
  return openBlock(), createBlock(resolveDynamicComponent(e.computedTag), mergeProps({ class: "navbar-brand" }, e.computedLinkProps), {
    default: withCtx(() => [
      renderSlot(e.$slots, "default")
    ]),
    _: 3
  }, 16);
}
var Yi = _e(Xi, [["render", Ji]]);
var Zi = defineComponent({
  __name: "BNavbarNav",
  props: {
    align: null,
    fill: { default: false },
    justified: { default: false },
    small: { default: false },
    tag: { default: "ul" }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "fill")), l = r(toRef(t, "justified")), n = r(toRef(t, "small")), u = ot(toRef(t, "align")), f = computed(() => ({
      "nav-fill": a.value,
      "nav-justified": l.value,
      [u.value]: t.align !== void 0,
      small: n.value
    }));
    return (c, v) => (openBlock(), createElementBlock("ul", {
      class: normalizeClass(["navbar-nav", unref(f)])
    }, [
      renderSlot(c.$slots, "default")
    ], 2));
  }
});
var Qi = createBaseVNode("span", { class: "navbar-toggler-icon" }, null, -1);
var er = defineComponent({
  __name: "BNavbarToggle",
  props: {
    disabled: { default: false },
    label: { default: "Toggle navigation" },
    target: null
  },
  emits: ["click"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "disabled")), n = computed(() => ({
      disabled: l.value,
      "aria-label": a.label
    })), u = computed(() => ({
      disabled: l.value
    })), f = (c) => {
      l.value || t("click", c);
    };
    return (c, v) => withDirectives((openBlock(), createElementBlock("button", mergeProps({
      class: ["navbar-toggler", unref(u)],
      type: "button"
    }, unref(n), { onClick: f }), [
      renderSlot(c.$slots, "default", {}, () => [
        Qi
      ])
    ], 16)), [
      [unref(Yt), unref(l) ? void 0 : e.target]
    ]);
  }
});
var tr = ["data-bs-backdrop", "data-bs-scroll"];
var ar = {
  key: 0,
  class: "offcanvas-header"
};
var lr = {
  id: "offcanvasLabel",
  class: "offcanvas-title"
};
var nr = { class: "offcanvas-body" };
var or = { key: 1 };
var sr = defineComponent({
  __name: "BOffcanvas",
  props: {
    dismissLabel: { default: "Close" },
    modelValue: { default: false },
    bodyScrolling: { default: false },
    backdrop: { default: true },
    placement: { default: "start" },
    title: null,
    noHeaderClose: { default: false },
    noHeader: { default: false }
  },
  emits: ["update:modelValue", "show", "shown", "hide", "hidden"],
  setup(e, { emit: t }) {
    const a = e, l = r(toRef(a, "modelValue")), n = r(toRef(a, "bodyScrolling")), u = r(toRef(a, "backdrop")), f = r(toRef(a, "noHeaderClose")), c = r(toRef(a, "noHeader")), v = useSlots(), p = ref(), B = ref(), m = computed(() => !Ve(v.footer)), $ = computed(() => [`offcanvas-${a.placement}`]), V = () => {
      t("show"), t("update:modelValue", true);
    }, T = () => {
      t("hide"), t("update:modelValue", false);
    };
    return watch(
      () => l.value,
      (b) => {
        var k, y;
        b ? (k = B.value) == null || k.show(p.value) : (y = B.value) == null || y.hide();
      }
    ), Be(p, "shown.bs.offcanvas", () => t("shown")), Be(p, "hidden.bs.offcanvas", () => t("hidden")), Be(p, "show.bs.offcanvas", () => {
      V();
    }), Be(p, "hide.bs.offcanvas", () => {
      T();
    }), onMounted(() => {
      var b;
      B.value = new Offcanvas(p.value), l.value && ((b = B.value) == null || b.show(p.value));
    }), (b, k) => (openBlock(), createElementBlock("div", {
      ref_key: "element",
      ref: p,
      class: normalizeClass(["offcanvas", unref($)]),
      tabindex: "-1",
      "aria-labelledby": "offcanvasLabel",
      "data-bs-backdrop": unref(u),
      "data-bs-scroll": unref(n)
    }, [
      unref(c) ? createCommentVNode("", true) : (openBlock(), createElementBlock("div", ar, [
        renderSlot(b.$slots, "header", normalizeProps(guardReactiveProps({ visible: unref(l), placement: e.placement, hide: T })), () => [
          createBaseVNode("h5", lr, [
            renderSlot(b.$slots, "title", {}, () => [
              createTextVNode(toDisplayString(e.title), 1)
            ])
          ]),
          unref(f) ? createCommentVNode("", true) : (openBlock(), createBlock(Ze, {
            key: 0,
            class: "text-reset",
            "data-bs-dismiss": "offcanvas",
            "aria-label": e.dismissLabel
          }, null, 8, ["aria-label"]))
        ])
      ])),
      createBaseVNode("div", nr, [
        renderSlot(b.$slots, "default")
      ]),
      unref(m) ? (openBlock(), createElementBlock("div", or, [
        renderSlot(b.$slots, "footer", normalizeProps(guardReactiveProps({ visible: unref(l), placement: e.placement, hide: T })))
      ])) : createCommentVNode("", true)
    ], 10, tr));
  }
});
var ir = defineComponent({
  __name: "BOverlay",
  props: {
    bgColor: null,
    blur: { default: "2px" },
    fixed: { default: false },
    noCenter: { default: false },
    noFade: { default: false },
    noWrap: { default: false },
    opacity: { default: 0.85 },
    overlayTag: { default: "div" },
    rounded: { type: [Boolean, String], default: false },
    show: { default: false },
    spinnerSmall: { default: false },
    spinnerType: { default: "border" },
    spinnerVariant: null,
    variant: { default: "light" },
    wrapTag: { default: "div" },
    zIndex: { default: 10 }
  },
  emits: ["click", "hidden", "shown"],
  setup(e, { emit: t }) {
    const a = e, l = { top: 0, left: 0, bottom: 0, right: 0 }, n = r(toRef(a, "fixed")), u = r(toRef(a, "noCenter")), f = r(toRef(a, "noWrap")), c = r(toRef(a, "show")), v = r(toRef(a, "spinnerSmall")), p = computed(
      () => a.rounded === true || a.rounded === "" ? "rounded" : a.rounded === false ? "" : `rounded-${a.rounded}`
    ), B = computed(
      () => a.variant && !a.bgColor ? `bg-${a.variant}` : ""
    ), m = computed(() => c.value ? "true" : null), $ = computed(() => ({
      type: a.spinnerType || void 0,
      variant: a.spinnerVariant || void 0,
      small: v.value
    })), V = computed(() => ({
      ...l,
      zIndex: a.zIndex || 10
    })), T = computed(() => [
      "b-overlay",
      {
        "position-absolute": !f.value || !n.value,
        "position-fixed": f.value && n.value
      }
    ]), b = computed(() => [B.value, p.value]), k = computed(() => ({
      ...l,
      opacity: a.opacity,
      backgroundColor: a.bgColor || void 0,
      backdropFilter: blur ? `blur(${blur})` : void 0
    })), y = computed(
      () => u.value ? l : {
        top: "50%",
        left: "50%",
        transform: "translateX(-50%) translateY(-50%)"
      }
    );
    return (C, q) => (openBlock(), createBlock(resolveDynamicComponent(e.wrapTag), {
      class: "b-overlay-wrap position-relative",
      "aria-busy": unref(m)
    }, {
      default: withCtx(() => [
        renderSlot(C.$slots, "default"),
        createVNode(St, {
          "no-fade": e.noFade,
          "trans-props": { enterToClass: "show" },
          name: "fade",
          onOnAfterEnter: q[1] || (q[1] = (E) => t("shown")),
          onOnAfterLeave: q[2] || (q[2] = (E) => t("hidden"))
        }, {
          default: withCtx(() => [
            unref(c) ? (openBlock(), createBlock(resolveDynamicComponent(e.overlayTag), {
              key: 0,
              class: normalizeClass(unref(T)),
              style: normalizeStyle(unref(V)),
              onClick: q[0] || (q[0] = (E) => t("click", E))
            }, {
              default: withCtx(() => [
                createBaseVNode("div", {
                  class: normalizeClass(["position-absolute", unref(b)]),
                  style: normalizeStyle(unref(k))
                }, null, 6),
                createBaseVNode("div", {
                  class: "position-absolute",
                  style: normalizeStyle(unref(y))
                }, [
                  renderSlot(C.$slots, "overlay", normalizeProps(guardReactiveProps(unref($))), () => [
                    createVNode(Ct, normalizeProps(guardReactiveProps(unref($))), null, 16)
                  ])
                ], 4)
              ]),
              _: 3
            }, 8, ["class", "style"])) : createCommentVNode("", true)
          ]),
          _: 3
        }, 8, ["no-fade"])
      ]),
      _: 3
    }, 8, ["aria-busy"]));
  }
});
var rr = 5;
var _l = 20;
var Vl = 0;
var Oe = 3;
var ur = "ellipsis-text";
var dr = "first-text";
var cr = "last-text";
var fr = "next-text";
var vr = "page";
var mr = "prev-text";
var pa = (e) => Math.max(Le(e) || _l, 1);
var ha = (e) => Math.max(Le(e) || Vl, 0);
var br = (e, t) => {
  const a = Le(e) || 1;
  return a > t ? t : a < 1 ? 1 : a;
};
var gr = defineComponent({
  name: "BPagination",
  props: {
    align: { type: String, default: "start" },
    ariaControls: { type: String, required: false },
    ariaLabel: { type: String, default: "Pagination" },
    disabled: { type: [Boolean, String], default: false },
    ellipsisClass: { type: [Array, String], default: () => [] },
    ellipsisText: { type: String, default: "…" },
    firstClass: { type: [Array, String], default: () => [] },
    firstNumber: { type: [Boolean, String], default: false },
    firstText: { type: String, default: "«" },
    hideEllipsis: { type: [Boolean, String], default: false },
    hideGotoEndButtons: { type: [Boolean, String], default: false },
    labelFirstPage: { type: String, default: "Go to first page" },
    labelLastPage: { type: String, default: "Go to last page" },
    labelNextPage: { type: String, default: "Go to next page" },
    labelPage: { type: String, default: "Go to page" },
    labelPrevPage: { type: String, default: "Go to previous page" },
    lastClass: { type: [Array, String], default: () => [] },
    lastNumber: { type: [Boolean, String], default: false },
    lastText: { type: String, default: "»" },
    limit: { type: Number, default: rr },
    modelValue: { type: Number, default: 1 },
    nextClass: { type: [Array, String], default: () => [] },
    nextText: { type: String, default: "›" },
    pageClass: { type: [Array, String], default: () => [] },
    perPage: { type: Number, default: _l },
    pills: { type: [Boolean, String], default: false },
    prevClass: { type: [Array, String], default: () => [] },
    prevText: { type: String, default: "‹" },
    size: { type: String, required: false },
    totalRows: { type: Number, default: Vl }
  },
  emits: ["update:modelValue", "page-click"],
  setup(e, { emit: t, slots: a }) {
    const l = r(toRef(e, "disabled")), n = r(toRef(e, "firstNumber")), u = r(toRef(e, "hideEllipsis")), f = r(toRef(e, "hideGotoEndButtons")), c = r(toRef(e, "lastNumber")), v = r(toRef(e, "pills")), p = computed(
      () => e.align === "fill" ? "start" : e.align
    ), B = ot(toRef(p, "value")), m = computed(
      () => Math.ceil(ha(e.totalRows) / pa(e.perPage))
    ), $ = computed(() => {
      let I;
      return m.value - e.modelValue + 2 < e.limit && e.limit > Oe ? I = m.value - T.value + 1 : I = e.modelValue - Math.floor(T.value / 2), I < 1 ? I = 1 : I > m.value - T.value && (I = m.value - T.value + 1), e.limit <= Oe && c.value && m.value === I + T.value - 1 && (I = Math.max(I - 1, 1)), I;
    }), V = computed(() => {
      const I = m.value - e.modelValue;
      let _ = false;
      return I + 2 < e.limit && e.limit > Oe ? e.limit > Oe && (_ = true) : e.limit > Oe && (_ = !!(!u.value || n.value)), $.value <= 1 && (_ = false), _ && n.value && $.value < 4 && (_ = false), _;
    }), T = computed(() => {
      let I = e.limit;
      return m.value <= e.limit ? I = m.value : e.modelValue < e.limit - 1 && e.limit > Oe ? ((!u.value || c.value) && (I = e.limit - (n.value ? 0 : 1)), I = Math.min(I, e.limit)) : m.value - e.modelValue + 2 < e.limit && e.limit > Oe ? (!u.value || n.value) && (I = e.limit - (c.value ? 0 : 1)) : e.limit > Oe && (I = e.limit - (u.value ? 0 : 2)), I;
    }), b = computed(() => {
      const I = m.value - T.value;
      let _ = false;
      e.modelValue < e.limit - 1 && e.limit > Oe ? (!u.value || c.value) && (_ = true) : e.limit > Oe && (_ = !!(!u.value || c.value)), $.value > I && (_ = false);
      const z = $.value + T.value - 1;
      return _ && c.value && z > m.value - 3 && (_ = false), _;
    }), k = reactive({
      pageSize: pa(e.perPage),
      totalRows: ha(e.totalRows),
      numberOfPages: m.value
    }), y = (I, _) => {
      if (_ === e.modelValue)
        return;
      const { target: z } = I, x = new Xe("page-click", {
        cancelable: true,
        target: z
      });
      t("page-click", x, _), !x.defaultPrevented && t("update:modelValue", _);
    }, C = computed(() => e.size ? `pagination-${e.size}` : ""), q = computed(() => v.value ? "b-pagination-pills" : "");
    watch(
      () => e.modelValue,
      (I) => {
        const _ = br(I, m.value);
        _ !== e.modelValue && t("update:modelValue", _);
      }
    ), watch(k, (I, _) => {
      I != null && (_.pageSize !== I.pageSize && _.totalRows === I.totalRows || _.numberOfPages !== I.numberOfPages && e.modelValue > _.numberOfPages) && t("update:modelValue", 1);
    });
    const E = computed(() => {
      const I = [];
      for (let _ = 0; _ < T.value; _++)
        I.push({ number: $.value + _, classes: null });
      return I;
    });
    return () => {
      const I = [], _ = E.value.map((K) => K.number), z = (K) => K === e.modelValue, x = e.modelValue < 1, w = e.align === "fill", P = (K, me, ae, ge, ye, pe) => {
        const re = l.value || z(pe) || x || K < 1 || K > m.value, H = K < 1 ? 1 : K > m.value ? m.value : K, N = { disabled: re, page: H, index: H - 1 }, X = Fe(ae, N, a) || ge || "";
        return h(
          "li",
          {
            class: [
              "page-item",
              {
                disabled: re,
                "flex-fill": w,
                "d-flex": w && !re
              },
              ye
            ]
          },
          h(
            re ? "span" : "button",
            {
              class: ["page-link", { "flex-grow-1": !re && w }],
              "aria-label": me,
              "aria-controls": e.ariaControls || null,
              "aria-disabled": re ? "true" : null,
              role: "menuitem",
              type: re ? null : "button",
              tabindex: re ? null : "-1",
              onClick: (ne) => {
                re || y(ne, H);
              }
            },
            X
          )
        );
      }, L = (K) => h(
        "li",
        {
          class: [
            "page-item",
            "disabled",
            "bv-d-xs-down-none",
            w ? "flex-fill" : "",
            e.ellipsisClass
          ],
          role: "separator",
          key: `ellipsis-${K ? "last" : "first"}`
        },
        [
          h(
            "span",
            { class: ["page-link"] },
            Fe(ur, {}, a) || e.ellipsisText || "..."
          )
        ]
      ), te = (K, me) => {
        const ae = z(K.number) && !x, ge = l.value ? null : ae || x && me === 0 ? "0" : "-1", ye = {
          active: ae,
          disabled: l.value,
          page: K.number,
          index: K.number - 1,
          content: K.number
        }, pe = Fe(vr, ye, a) || K.number, re = h(
          l.value ? "span" : "button",
          {
            class: ["page-link", { "flex-grow-1": !l.value && w }],
            "aria-controls": e.ariaControls || null,
            "aria-disabled": l.value ? "true" : null,
            "aria-label": e.labelPage ? `${e.labelPage} ${K.number}` : null,
            role: "menuitemradio",
            type: l.value ? null : "button",
            tabindex: ge,
            onClick: (H) => {
              l.value || y(H, K.number);
            }
          },
          pe
        );
        return h(
          "li",
          {
            class: [
              "page-item",
              {
                disabled: l.value,
                active: ae,
                "flex-fill": w,
                "d-flex": w && !l.value
              },
              e.pageClass
            ],
            role: "presentation",
            key: `page-${K.number}`
          },
          re
        );
      };
      if (!f.value && !n.value) {
        const K = P(
          1,
          e.labelFirstPage,
          dr,
          e.firstText,
          e.firstClass,
          1
        );
        I.push(K);
      }
      const Q = P(
        e.modelValue - 1,
        e.labelFirstPage,
        mr,
        e.prevText,
        e.prevClass,
        1
      );
      I.push(Q), n.value && _[0] !== 1 && I.push(te({ number: 1 }, 0)), V.value && I.push(L(false)), E.value.forEach((K, me) => {
        const ae = V.value && n.value && _[0] !== 1 ? 1 : 0;
        I.push(te(K, me + ae));
      }), b.value && I.push(L(true)), c.value && _[_.length - 1] !== m.value && I.push(te({ number: m.value }, -1));
      const ie = P(
        e.modelValue + 1,
        e.labelNextPage,
        fr,
        e.nextText,
        e.nextClass,
        m.value
      );
      if (I.push(ie), !c.value && !f.value) {
        const K = P(
          m.value,
          e.labelLastPage,
          cr,
          e.lastText,
          e.lastClass,
          m.value
        );
        I.push(K);
      }
      return h(
        "ul",
        {
          class: ["pagination", C.value, B.value, q.value],
          role: "menubar",
          "aria-disabled": l.value,
          "aria-label": e.ariaLabel || null
        },
        I
      );
    };
  }
});
var Ie = defineComponent({
  __name: "BPlaceholder",
  props: {
    tag: { default: "span" },
    width: null,
    cols: null,
    variant: null,
    size: null,
    animation: null
  },
  setup(e) {
    const t = e, a = computed(
      () => t.width === void 0 ? void 0 : typeof t.width == "number" ? t.width.toString() : t.width.includes("%") ? t.width.replaceAll("%", "") : t.width
    ), l = computed(
      () => t.cols === void 0 ? void 0 : typeof t.cols == "number" ? t.cols.toString() : t.cols
    ), n = computed(() => ({
      [`col-${l.value}`]: l.value !== void 0 && a.value === void 0,
      [`bg-${t.variant}`]: t.variant !== void 0,
      [`placeholder-${t.size}`]: t.size !== void 0,
      [`placeholder-${t.animation}`]: t.animation !== void 0
    })), u = computed(
      () => a.value === void 0 ? void 0 : `width: ${a.value}%;`
    );
    return (f, c) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      class: normalizeClass(["placeholder", unref(n)]),
      style: normalizeStyle(unref(u))
    }, null, 8, ["class", "style"]));
  }
});
var Al = defineComponent({
  __name: "BPlaceholderButton",
  props: {
    tag: { default: "div" },
    width: null,
    cols: null,
    variant: { default: "primary" },
    animation: null
  },
  setup(e) {
    const t = e, a = computed(() => ["btn", `btn-${t.variant}`, "disabled"]), l = computed(() => ({
      animation: t.animation,
      width: t.width,
      cols: t.cols,
      tag: t.tag
    }));
    return (n, u) => (openBlock(), createBlock(Ie, mergeProps({ class: unref(a) }, unref(l)), null, 16, ["class"]));
  }
});
var pr = defineComponent({
  __name: "BPlaceholderCard",
  props: {
    noHeader: { default: false },
    headerWidth: { default: 100 },
    headerVariant: null,
    headerAnimation: null,
    headerSize: null,
    noFooter: { default: false },
    footerWidth: { default: 100 },
    footerVariant: null,
    footerAnimation: null,
    footerSize: null,
    animation: null,
    size: null,
    variant: null,
    noButton: { default: false },
    imgBottom: { default: false },
    imgSrc: null,
    imgBlankColor: { default: "#868e96" },
    imgHeight: { default: 100 },
    noImg: { default: false }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "noButton")), l = r(toRef(t, "noHeader")), n = r(toRef(t, "noFooter")), u = r(toRef(t, "noImg")), f = computed(() => ({
      width: t.headerWidth,
      variant: t.headerVariant,
      animation: t.headerAnimation,
      size: t.headerSize
    })), c = computed(() => ({
      width: t.footerWidth,
      animation: t.footerAnimation,
      size: a.value ? t.footerSize : void 0,
      variant: t.footerVariant
    })), v = computed(() => ({
      blank: !t.imgSrc,
      blankColor: t.imgBlankColor,
      height: t.imgSrc ? void 0 : t.imgHeight,
      src: t.imgSrc,
      top: !t.imgBottom,
      bottom: t.imgBottom
    }));
    return (p, B) => (openBlock(), createBlock(vl, { "img-bottom": e.imgBottom }, createSlots({
      default: withCtx(() => [
        renderSlot(p.$slots, "default", {}, () => [
          createVNode(Ie, { cols: "7" }),
          createVNode(Ie, { cols: "4" }),
          createVNode(Ie, { cols: "4" }),
          createVNode(Ie, { cols: "6" }),
          createVNode(Ie, { cols: "8" })
        ])
      ]),
      _: 2
    }, [
      unref(u) ? void 0 : {
        name: "img",
        fn: withCtx(() => [
          renderSlot(p.$slots, "img", {}, () => [
            createVNode(pt, normalizeProps(guardReactiveProps(unref(v))), null, 16)
          ])
        ]),
        key: "0"
      },
      unref(l) ? void 0 : {
        name: "header",
        fn: withCtx(() => [
          renderSlot(p.$slots, "header", {}, () => [
            createVNode(Ie, normalizeProps(guardReactiveProps(unref(f))), null, 16)
          ])
        ]),
        key: "1"
      },
      unref(n) ? void 0 : {
        name: "footer",
        fn: withCtx(() => [
          renderSlot(p.$slots, "footer", {}, () => [
            unref(a) ? (openBlock(), createBlock(Ie, normalizeProps(mergeProps({ key: 1 }, unref(c))), null, 16)) : (openBlock(), createBlock(Al, normalizeProps(mergeProps({ key: 0 }, unref(c))), null, 16))
          ])
        ]),
        key: "2"
      }
    ]), 1032, ["img-bottom"]));
  }
});
var wt = defineComponent({
  __name: "BTableSimple",
  props: {
    bordered: { default: false },
    borderless: { default: false },
    borderVariant: null,
    captionTop: { default: false },
    dark: { default: false },
    hover: { default: false },
    responsive: { type: [Boolean, String], default: false },
    stacked: { type: [Boolean, String], default: false },
    striped: { default: false },
    small: { default: false },
    tableClass: null,
    tableVariant: null,
    stickyHeader: { default: false }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "captionTop")), l = r(toRef(t, "borderless")), n = r(toRef(t, "bordered")), u = r(toRef(t, "dark")), f = r(toRef(t, "hover")), c = r(toRef(t, "small")), v = r(toRef(t, "striped")), p = r(toRef(t, "stickyHeader")), B = computed(() => [
      "table",
      "b-table",
      {
        "table-bordered": n.value,
        "table-borderless": l.value,
        [`border-${t.borderVariant}`]: t.borderVariant !== void 0,
        "caption-top": a.value,
        "table-dark": u.value,
        "table-hover": f.value,
        "b-table-stacked": typeof t.stacked == "boolean" && t.stacked,
        [`b-table-stacked-${t.stacked}`]: typeof t.stacked == "string",
        "table-striped": v.value,
        "table-sm": c.value,
        [`table-${t.tableVariant}`]: t.tableVariant !== void 0
      },
      t.tableClass
    ]), m = computed(() => [
      {
        "table-responsive": t.responsive === true,
        [`table-responsive-${t.responsive}`]: typeof t.responsive == "string",
        "b-table-sticky-header": p.value
      }
    ]);
    return ($, V) => e.responsive ? (openBlock(), createElementBlock("div", {
      key: 1,
      class: normalizeClass(unref(m))
    }, [
      createBaseVNode("table", {
        role: "table",
        class: normalizeClass(unref(B))
      }, [
        renderSlot($.$slots, "default")
      ], 2)
    ], 2)) : (openBlock(), createElementBlock("table", {
      key: 0,
      role: "table",
      class: normalizeClass(unref(B))
    }, [
      renderSlot($.$slots, "default")
    ], 2));
  }
});
var hr = defineComponent({
  __name: "BPlaceholderTable",
  props: {
    rows: { default: 3 },
    columns: { default: 5 },
    cellWidth: { default: 100 },
    size: null,
    animation: null,
    variant: null,
    headerColumns: null,
    hideHeader: { default: false },
    headerCellWidth: { default: 100 },
    headerSize: null,
    headerAnimation: null,
    headerVariant: null,
    footerColumns: null,
    showFooter: { default: false },
    footerCellWidth: { default: 100 },
    footerSize: null,
    footerAnimation: null,
    footerVariant: null
  },
  setup(e) {
    const t = e, a = computed(
      () => typeof t.columns == "string" ? Qe(t.columns, 5) : t.columns
    ), l = computed(
      () => typeof t.rows == "string" ? Qe(t.rows, 3) : t.rows
    ), n = computed(
      () => t.headerColumns === void 0 ? a.value : typeof t.headerColumns == "string" ? Qe(t.headerColumns, a.value) : t.headerColumns
    ), u = computed(
      () => t.footerColumns === void 0 ? a.value : typeof t.footerColumns == "string" ? Qe(t.footerColumns, a.value) : t.footerColumns
    ), f = computed(() => ({
      size: t.size,
      variant: t.variant,
      animation: t.animation,
      width: t.cellWidth
    })), c = computed(() => ({
      size: t.headerSize,
      variant: t.headerVariant,
      animation: t.headerAnimation,
      width: t.headerCellWidth
    })), v = computed(() => ({
      size: t.footerSize,
      variant: t.footerVariant,
      animation: t.footerAnimation,
      width: t.footerCellWidth
    })), p = r(toRef(t, "hideHeader")), B = r(toRef(t, "showFooter"));
    return (m, $) => (openBlock(), createBlock(wt, null, {
      default: withCtx(() => [
        unref(p) ? createCommentVNode("", true) : renderSlot(m.$slots, "thead", { key: 0 }, () => [
          createBaseVNode("thead", null, [
            createBaseVNode("tr", null, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(n), (V, T) => (openBlock(), createElementBlock("th", { key: T }, [
                createVNode(Ie, normalizeProps(guardReactiveProps(unref(c))), null, 16)
              ]))), 128))
            ])
          ])
        ]),
        renderSlot(m.$slots, "default", {}, () => [
          createBaseVNode("tbody", null, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l), (V, T) => (openBlock(), createElementBlock("tr", { key: T }, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(a), (b, k) => (openBlock(), createElementBlock("td", { key: k }, [
                createVNode(Ie, normalizeProps(guardReactiveProps(unref(f))), null, 16)
              ]))), 128))
            ]))), 128))
          ])
        ]),
        unref(B) ? renderSlot(m.$slots, "tfoot", { key: 1 }, () => [
          createBaseVNode("tfoot", null, [
            createBaseVNode("tr", null, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(u), (V, T) => (openBlock(), createElementBlock("th", { key: T }, [
                createVNode(Ie, normalizeProps(guardReactiveProps(unref(v))), null, 16)
              ]))), 128))
            ])
          ])
        ]) : createCommentVNode("", true)
      ]),
      _: 3
    }));
  }
});
var yr = defineComponent({
  __name: "BPlaceholderWrapper",
  props: {
    loading: { default: false }
  },
  setup(e) {
    const a = r(toRef(e, "loading"));
    return (l, n) => unref(a) ? renderSlot(l.$slots, "loading", { key: 0 }) : renderSlot(l.$slots, "default", { key: 1 });
  }
});
var Br = defineComponent({
  props: {
    container: {
      type: [String, Object],
      default: "body"
    },
    content: { type: String },
    id: { type: String },
    customClass: { type: String, default: "" },
    noninteractive: { type: [Boolean, String], default: false },
    placement: { type: String, default: "right" },
    target: {
      type: [String, Object],
      default: void 0
    },
    title: { type: String },
    delay: { type: [Number, Object], default: 0 },
    triggers: { type: String, default: "click" },
    show: { type: [Boolean, String], default: false },
    variant: { type: String, default: void 0 },
    html: { type: [Boolean, String], default: true },
    sanitize: { type: [Boolean, String], default: false },
    offset: { type: String, default: "0" }
  },
  emits: ["show", "shown", "hide", "hidden", "inserted"],
  setup(e, { emit: t, slots: a }) {
    r(toRef(e, "noninteractive"));
    const l = r(toRef(e, "show")), n = r(toRef(e, "html")), u = r(toRef(e, "sanitize")), f = ref(), c = ref(), v = ref(), p = ref(), B = ref(), m = computed(() => ({
      [`b-popover-${e.variant}`]: e.variant !== void 0
    })), $ = (b) => {
      if (typeof b == "string")
        return b;
      if (b instanceof HTMLElement)
        return b;
      if (typeof b < "u")
        return b.$el;
    }, V = (b) => {
      if (!!b) {
        if (typeof b == "string") {
          const k = document.getElementById(b);
          return k || void 0;
        }
        return b;
      }
    }, T = (b) => {
      c.value = V($(b)), c.value && (v.value = new Popover(c.value, {
        customClass: e.customClass,
        container: $(e.container),
        trigger: e.triggers,
        placement: e.placement,
        title: e.title || a.title ? p.value : "",
        content: B.value,
        html: n.value,
        delay: e.delay,
        sanitize: u.value,
        offset: e.offset
      }));
    };
    return watch(
      () => e.target,
      (b) => {
        var k;
        (k = v.value) == null || k.dispose(), T(b);
      }
    ), watch(
      () => l.value,
      (b, k) => {
        var y, C;
        b !== k && (b ? (y = v.value) == null || y.show() : (C = v.value) == null || C.hide());
      }
    ), Be(c, "show.bs.popover", () => t("show")), Be(c, "shown.bs.popover", () => t("shown")), Be(c, "hide.bs.popover", () => t("hide")), Be(c, "hidden.bs.popover", () => t("hidden")), Be(c, "inserted.bs.popover", () => t("inserted")), onMounted(() => {
      var b, k, y;
      nextTick(() => {
        T(e.target);
      }), (k = (b = f.value) == null ? void 0 : b.parentNode) == null || k.removeChild(f.value), l.value && ((y = v.value) == null || y.show());
    }), onBeforeUnmount(() => {
      var b;
      (b = v.value) == null || b.dispose();
    }), {
      element: f,
      titleRef: p,
      contentRef: B,
      computedClasses: m
    };
  }
});
var $r = ["id"];
var kr = { ref: "titleRef" };
var Cr = { ref: "contentRef" };
function Sr(e, t, a, l, n, u) {
  return openBlock(), createElementBlock("div", {
    id: e.id,
    ref: "element",
    class: normalizeClass(["popover b-popover", e.computedClasses]),
    role: "tooltip",
    tabindex: "-1"
  }, [
    createBaseVNode("div", kr, [
      renderSlot(e.$slots, "title", {}, () => [
        createTextVNode(toDisplayString(e.title), 1)
      ])
    ], 512),
    createBaseVNode("div", Cr, [
      renderSlot(e.$slots, "default", {}, () => [
        createTextVNode(toDisplayString(e.content), 1)
      ])
    ], 512)
  ], 10, $r);
}
var wr = _e(Br, [["render", Sr]]);
var Tr = ["aria-valuenow", "aria-valuemax"];
var xl = defineComponent({
  __name: "BProgressBar",
  props: {
    animated: { default: false },
    label: null,
    labelHtml: null,
    max: null,
    precision: { default: 0 },
    showProgress: { default: false },
    showValue: { default: false },
    striped: { default: false },
    value: { default: 0 },
    variant: null
  },
  setup(e) {
    const t = e, a = inject(Il), l = r(toRef(t, "animated")), n = r(toRef(t, "showProgress")), u = r(toRef(t, "showValue")), f = r(toRef(t, "striped")), c = computed(() => ({
      "progress-bar-animated": l.value || (a == null ? void 0 : a.animated),
      "progress-bar-striped": f.value || (a == null ? void 0 : a.striped) || l.value || (a == null ? void 0 : a.animated),
      [`bg-${t.variant}`]: t.variant !== void 0
    })), v = computed(
      () => typeof t.precision == "number" ? t.precision : Number.parseFloat(t.precision)
    ), p = computed(
      () => typeof t.value == "number" ? t.value : Number.parseFloat(t.value)
    ), B = computed(
      () => typeof t.max == "number" ? t.max : t.max === void 0 ? void 0 : Number.parseFloat(t.max)
    ), m = computed(
      () => t.labelHtml !== void 0 ? t.labelHtml : u.value || (a == null ? void 0 : a.showValue) ? p.value.toFixed(v.value) : n.value || (a == null ? void 0 : a.showProgress) ? (p.value * 100 / (B.value || 100)).toFixed(v.value) : t.label !== void 0 ? t.label : ""
    ), $ = computed(
      () => a != null && a.max ? `${p.value * 100 / (typeof a.max == "number" ? a.max : Number.parseInt(a.max))}%` : t.max ? `${p.value * 100 / (typeof t.max == "number" ? t.max : Number.parseInt(t.max))}%` : typeof t.value == "string" ? t.value : `${t.value}%`
    );
    return (V, T) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["progress-bar", unref(c)]),
      role: "progressbar",
      "aria-valuenow": e.value,
      "aria-valuemin": "0",
      "aria-valuemax": e.max,
      style: normalizeStyle({ width: unref($) })
    }, [
      renderSlot(V.$slots, "default", {}, () => [
        createTextVNode(toDisplayString(unref(m)), 1)
      ])
    ], 14, Tr));
  }
});
var Il = Symbol();
var _r = defineComponent({
  __name: "BProgress",
  props: {
    variant: null,
    max: null,
    height: null,
    animated: { default: false },
    precision: { default: 0 },
    showProgress: { default: false },
    showValue: { default: false },
    striped: { default: false },
    value: { default: 0 }
  },
  setup(e) {
    const t = e, a = r(toRef(t, "animated")), l = r(toRef(t, "showProgress")), n = r(toRef(t, "showValue")), u = r(toRef(t, "striped")), f = computed(() => ({
      animated: t.animated,
      max: t.max,
      precision: t.precision,
      showProgress: t.showProgress,
      showValue: t.showValue,
      striped: t.striped,
      value: t.value,
      variant: t.variant
    }));
    return provide(Il, {
      animated: a.value,
      max: t.max,
      showProgress: l.value,
      showValue: n.value,
      striped: u.value
    }), (c, v) => (openBlock(), createElementBlock("div", {
      class: "progress",
      style: normalizeStyle({ height: e.height })
    }, [
      renderSlot(c.$slots, "default", {}, () => [
        createVNode(xl, normalizeProps(guardReactiveProps(unref(f))), null, 16)
      ])
    ], 4));
  }
});
var ya = Bt("cols", [""], { type: [String, Number], default: null });
var Vr = defineComponent({
  name: "BRow",
  props: {
    tag: { type: String, default: "div" },
    gutterX: { type: String, default: null },
    gutterY: { type: String, default: null },
    noGutters: { type: [Boolean, String], default: false },
    alignV: { type: String, default: null },
    alignH: { type: String, default: null },
    alignContent: { type: String, default: null },
    ...ya
  },
  setup(e) {
    const t = r(toRef(e, "noGutters")), a = ot(toRef(e, "alignH")), l = computed(() => ja(e, ya, "cols", "row-cols"));
    return {
      computedClasses: computed(() => [
        l.value,
        {
          [`gx-${e.gutterX}`]: e.gutterX !== null,
          [`gy-${e.gutterY}`]: e.gutterY !== null,
          "g-0": t.value,
          [`align-items-${e.alignV}`]: e.alignV !== null,
          [a.value]: e.alignH !== null,
          [`align-content-${e.alignContent}`]: e.alignContent !== null
        }
      ])
    };
  }
});
function Ar(e, t, a, l, n, u) {
  return openBlock(), createBlock(resolveDynamicComponent(e.tag), {
    class: normalizeClass(["row", e.computedClasses])
  }, {
    default: withCtx(() => [
      renderSlot(e.$slots, "default")
    ]),
    _: 3
  }, 8, ["class"]);
}
var xr = _e(Vr, [["render", Ar]]);
var vt = defineComponent({
  __name: "BSkeleton",
  props: {
    height: null,
    width: null,
    size: null,
    animation: { default: "wave" },
    type: { default: "text" },
    variant: null
  },
  setup(e) {
    const t = e, a = computed(() => [
      `b-skeleton-${t.type}`,
      {
        [`b-skeleton-animate-${t.animation}`]: typeof t.animation == "boolean" ? false : t.animation,
        [`bg-${t.variant}`]: t.variant !== void 0
      }
    ]), l = computed(() => ({
      width: t.size || t.width,
      height: t.size || t.height
    }));
    return (n, u) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["b-skeleton", unref(a)]),
      style: normalizeStyle(unref(l))
    }, null, 6));
  }
});
var Ir = defineComponent({
  __name: "BSkeletonIcon",
  props: {
    animation: { default: "wave" }
  },
  setup(e) {
    const t = e, a = computed(() => [`b-skeleton-animate-${t.animation}`]);
    return (l, n) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["b-skeleton-icon-wrapper position-relative d-inline-block overflow-hidden", unref(a)])
    }, [
      renderSlot(l.$slots, "default")
    ], 2));
  }
});
var Fr = { key: 0 };
var Pr = { key: 1 };
var Or = defineComponent({
  __name: "BSkeletonTable",
  props: {
    animation: { default: "wave" },
    columns: { default: 5 },
    hideHeader: { default: false },
    rows: { default: 3 },
    showFooter: { default: false },
    tableProps: null
  },
  setup(e) {
    const t = e, a = r(toRef(t, "hideHeader")), l = r(toRef(t, "showFooter"));
    return (n, u) => (openBlock(), createBlock(wt, normalizeProps(guardReactiveProps(e.tableProps)), {
      default: withCtx(() => [
        unref(a) ? createCommentVNode("", true) : (openBlock(), createElementBlock("thead", Fr, [
          createBaseVNode("tr", null, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(e.columns, (f, c) => (openBlock(), createElementBlock("th", { key: c }, [
              createVNode(vt)
            ]))), 128))
          ])
        ])),
        createBaseVNode("tbody", null, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(e.rows, (f, c) => (openBlock(), createElementBlock("tr", { key: c }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(e.columns, (v, p) => (openBlock(), createElementBlock("td", { key: p }, [
              createVNode(vt, { width: "75%" })
            ]))), 128))
          ]))), 128))
        ]),
        unref(l) ? (openBlock(), createElementBlock("tfoot", Pr, [
          createBaseVNode("tr", null, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(e.columns, (f, c) => (openBlock(), createElementBlock("th", { key: c }, [
              createVNode(vt)
            ]))), 128))
          ])
        ])) : createCommentVNode("", true)
      ]),
      _: 1
    }, 16));
  }
});
var Lr = defineComponent({
  __name: "BSkeletonWrapper",
  props: {
    loading: { default: false }
  },
  setup(e) {
    const a = r(toRef(e, "loading"));
    return (l, n) => unref(a) ? renderSlot(l.$slots, "loading", { key: 0 }) : renderSlot(l.$slots, "default", { key: 1 });
  }
});
var Ba = [
  "ar",
  "az",
  "ckb",
  "fa",
  "he",
  "ks",
  "lrc",
  "mzn",
  "ps",
  "sd",
  "te",
  "ug",
  "ur",
  "yi"
].map((e) => e.toLowerCase());
var zr = (e) => {
  const t = mt(e).toLowerCase().replace(vn, "").split("-"), a = t.slice(0, 2).join("-"), l = t[0];
  return Ba.includes(a) || Ba.includes(l);
};
var Nr = (e) => hn ? Pt(e) ? e : { capture: !!e || false } : !!(Pt(e) ? e.capture : e);
var Er = (e, t, a, l) => {
  e && e.addEventListener && e.addEventListener(t, a, Nr(l));
};
var Dr = (e, t, a, l) => {
  e && e.removeEventListener && e.removeEventListener(t, a, l);
};
var $a = (e, t) => {
  (e ? Er : Dr)(...t);
};
var rt = (e, { preventDefault: t = true, propagation: a = true, immediatePropagation: l = false } = {}) => {
  t && e.preventDefault(), a && e.stopPropagation(), l && e.stopImmediatePropagation();
};
var qt = "ArrowDown";
var Fl = "End";
var Pl = "Home";
var Ol = "PageDown";
var Ll = "PageUp";
var Mt = "ArrowUp";
var ka = 1;
var Ca = 100;
var Sa = 1;
var wa = 500;
var Ta = 100;
var _a = 10;
var Va = 4;
var Aa = [Mt, qt, Pl, Fl, Ll, Ol];
var Hr = defineComponent({
  props: {
    ariaControls: { type: String, required: false },
    ariaLabel: { type: String, required: false },
    labelIncrement: { type: String, default: "Increment" },
    labelDecrement: { type: String, default: "Decrement" },
    modelValue: { type: Number, default: null },
    name: { type: String, default: "BFormSpinbutton" },
    disabled: { type: [Boolean, String], default: false },
    placeholder: { type: String, required: false },
    locale: { type: String, default: "locale" },
    form: { type: String, required: false },
    inline: { type: Boolean, default: false },
    size: { type: String, required: false },
    formatterFn: {
      type: Function
    },
    readonly: { type: Boolean, default: false },
    vertical: { type: Boolean, default: false },
    repeatDelay: {
      type: [String, Number],
      default: wa
    },
    repeatInterval: {
      type: [String, Number],
      default: Ta
    },
    repeatStepMultiplier: {
      type: [String, Number],
      default: Va
    },
    repeatThreshold: {
      type: [String, Number],
      default: _a
    },
    required: { type: [Boolean, String], default: false },
    step: { type: [String, Number], default: Sa },
    min: { type: [String, Number], default: ka },
    max: { type: [String, Number], default: Ca },
    wrap: { type: Boolean, default: false },
    state: { type: [Boolean, String], default: null }
  },
  emits: ["update:modelValue", "change"],
  setup(e, { emit: t }) {
    const a = ref(false), l = computed(() => 1), n = () => {
      t("change", f.value);
    }, u = ref(null), f = computed({
      get() {
        return Ne(e.modelValue) ? u.value : e.modelValue;
      },
      set(H) {
        Ne(e.modelValue) ? u.value = H : t("update:modelValue", H);
      }
    });
    let c, v, p = false;
    const B = computed(() => tt(e.step, Sa)), m = computed(() => tt(e.min, ka)), $ = computed(() => {
      const H = tt(e.max, Ca), N = B.value, X = m.value;
      return Math.floor((H - X) / N) * N + X;
    }), V = computed(() => {
      const H = Le(e.repeatDelay, 0);
      return H > 0 ? H : wa;
    }), T = computed(() => {
      const H = Le(e.repeatInterval, 0);
      return H > 0 ? H : Ta;
    }), b = computed(
      () => Math.max(Le(e.repeatThreshold, _a), 1)
    ), k = computed(
      () => Math.max(Le(e.repeatStepMultiplier, Va), 1)
    ), y = computed(() => {
      const H = B.value;
      return Math.floor(H) === H ? 0 : (H.toString().split(".")[1] || "").length;
    }), C = computed(() => Math.pow(10, y.value || 0)), q = computed(() => {
      const { value: H } = f;
      return H === null ? "" : H.toFixed(y.value);
    }), E = computed(() => {
      const H = [e.locale];
      return new Intl.NumberFormat(H).resolvedOptions().locale;
    }), I = computed(
      () => zr(E.value)
    ), _ = () => {
      const H = y.value;
      return new Intl.NumberFormat(E.value, {
        style: "decimal",
        useGrouping: false,
        minimumIntegerDigits: 1,
        minimumFractionDigits: H,
        maximumFractionDigits: H,
        notation: "standard"
      }).format;
    }, z = computed(
      () => e.formatterFn ? e.formatterFn : _()
    ), x = computed(() => ({
      role: "group",
      lang: E.value,
      tabindex: e.disabled ? null : "-1",
      title: e.ariaLabel
    })), w = computed(() => !Ne(e.modelValue) || !Ne(u.value)), P = computed(() => ({
      dir: I.value,
      spinId: l.value,
      tabindex: e.disabled ? null : "0",
      role: "spinbutton",
      "aria-live": "off",
      "aria-label": e.ariaLabel || null,
      "aria-controls": e.ariaControls || null,
      "aria-invalid": e.state === false || !w.value && e.required ? "true" : null,
      "aria-required": e.required ? "true" : null,
      "aria-valuemin": m.value,
      "aria-valuemax": $.value,
      "aria-valuenow": Ne(f.value) ? null : f.value,
      "aria-valuetext": Ne(f.value) ? null : z.value(f.value)
    })), L = (H) => {
      let { value: N } = f;
      if (!e.disabled && !Ne(N)) {
        const X = B.value * H, ne = m.value, Y = $.value, se = C.value, { wrap: Te } = e;
        N = Math.round((N - ne) / X) * X + ne + X, N = Math.round(N * se) / se, f.value = N > Y ? Te ? ne : Y : N < ne ? Te ? Y : ne : N;
      }
    }, te = (H = 1) => {
      Ne(f.value) ? f.value = m.value : L(1 * H);
    }, Q = (H = 1) => {
      Ne(f.value) ? f.value = e.wrap ? $.value : m.value : L(-1 * H);
    }, ie = (H) => {
      const { code: N, altKey: X, ctrlKey: ne, metaKey: Y } = H;
      if (!(e.disabled || e.readonly || X || ne || Y) && Aa.includes(N)) {
        if (rt(H, { propagation: false }), p)
          return;
        pe(), [Mt, qt].includes(N) ? (p = true, N === Mt ? me(H, te) : N === qt && me(H, Q)) : N === Ll ? te(k.value) : N === Ol ? Q(k.value) : N === Pl ? f.value = m.value : N === Fl && (f.value = $.value);
      }
    }, K = (H) => {
      const { code: N, altKey: X, ctrlKey: ne, metaKey: Y } = H;
      e.disabled || e.readonly || X || ne || Y || Aa.includes(N) && (rt(H, { propagation: false }), pe(), p = false, n());
    }, me = (H, N) => {
      const { type: X } = H || {};
      if (!e.disabled && !e.readonly) {
        if (ae(H) && X === "mousedown" && H.button)
          return;
        pe(), N(1);
        const ne = b.value, Y = k.value, se = V.value, Te = T.value;
        c = setTimeout(() => {
          let Ue = 0;
          v = setInterval(() => {
            N(Ue < ne ? 1 : Y), Ue++;
          }, Te);
        }, se);
      }
    };
    function ae(H) {
      return H.type === "mouseup" || H.type === "mousedown";
    }
    const ge = (H) => {
      ae(H) && H.type === "mouseup" && H.button || (rt(H, { propagation: false }), pe(), ye(false), n());
    }, ye = (H) => {
      try {
        $a(H, [document.body, "mouseup", ge, false]), $a(H, [document.body, "touchend", ge, false]);
      } catch {
        return 0;
      }
    }, pe = () => {
      clearTimeout(c), clearInterval(v), c = void 0, v = void 0;
    }, re = (H, N, X, ne, Y, se, Te) => {
      const Ue = h(X, {
        props: { scale: a.value ? 1.5 : 1.25 },
        attrs: { "aria-hidden": "true" }
      }), Tt = { hasFocus: a.value }, st = (Re) => {
        !e.disabled && !e.readonly && (rt(Re, { propagation: false }), ye(true), me(Re, H));
      };
      return h(
        "button",
        {
          class: [{ "py-0": !e.vertical }, "btn", "btn-sm", "border-0", "rounded-0"],
          tabindex: "-1",
          type: "button",
          disabled: e.disabled || e.readonly || se,
          "aria-disabled": e.disabled || e.readonly || se ? "true" : null,
          "aria-controls": l.value,
          "aria-label": N || null,
          "aria-keyshortcuts": Y || null,
          onmousedown: st,
          ontouchstart: st
        },
        [Fe(Te, Tt) || Ue]
      );
    };
    return () => {
      const H = re(
        te,
        e.labelIncrement,
        h(
          "svg",
          {
            xmlns: "http://www.w3.org/2000/svg",
            width: "16",
            height: "16",
            fill: "currentColor",
            class: "bi bi-plus",
            viewBox: "0 0 16 16"
          },
          h("path", {
            d: "M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"
          })
        ),
        "inc",
        "ArrowUp",
        false,
        "increment"
      ), N = re(
        Q,
        e.labelDecrement,
        h(
          "svg",
          {
            xmlns: "http://www.w3.org/2000/svg",
            width: "16",
            height: "16",
            fill: "currentColor",
            class: "bi bi-dash",
            viewBox: "0 0 16 16"
          },
          h("path", { d: "M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z" })
        ),
        "dec",
        "ArrowDown",
        false,
        "decrement"
      ), X = [];
      e.name && !e.disabled && X.push(
        h("input", {
          type: "hidden",
          name: e.name,
          form: e.form || null,
          value: q.value,
          key: "hidden"
        })
      );
      const ne = h(
        "output",
        {
          class: [
            { "d-flex": e.vertical },
            { "align-self-center": !e.vertical },
            { "align-items-center": e.vertical },
            { "border-top": e.vertical },
            { "border-bottom": e.vertical },
            { "border-start": !e.vertical },
            { "border-end": !e.vertical },
            "flex-grow-1"
          ],
          ...P.value,
          key: "output"
        },
        [
          h(
            "bdi",
            w.value ? z.value(f.value) : e.placeholder || ""
          )
        ]
      );
      return h(
        "div",
        {
          class: [
            "b-form-spinbutton form-control",
            { disabled: e.disabled },
            { readonly: e.readonly },
            { focus: a },
            { "d-inline-flex": e.inline || e.vertical },
            { "d-flex": !e.inline && !e.vertical },
            { "align-items-stretch": !e.vertical },
            { "flex-column": e.vertical },
            e.size ? `form-control-${e.size}` : null
          ],
          ...x.value,
          onkeydown: ie,
          onkeyup: K
        },
        e.vertical ? [H, X, ne, N] : [N, X, ne, H]
      );
    };
  }
});
var Rr = ["TD", "TH", "TR"];
var qr = [
  "a",
  "a *",
  "button",
  "button *",
  "input:not(.disabled):not([disabled])",
  "select:not(.disabled):not([disabled])",
  "textarea:not(.disabled):not([disabled])",
  '[role="link"]',
  '[role="link"] *',
  '[role="button"]',
  '[role="button"] *',
  "[tabindex]:not(.disabled):not([disabled])"
].join(",");
var ut = (e) => {
  if (!e || !e.target)
    return false;
  const t = e.target;
  if ("disabled" in t && t.disabled || Rr.indexOf(t.tagName) !== -1)
    return false;
  if (na(".dropdown-menu", t))
    return true;
  const a = t.tagName === "LABEL" ? t : na("label", t);
  if (a) {
    const l = Kt(a, "for"), n = l ? Vn(l) : qa("input, select, textarea", a);
    if (n && !n.disabled)
      return true;
  }
  return Ma(t, qr);
};
var Mr = () => {
  const e = (v, p) => {
    const B = [];
    return !(v != null && v.length) && (p == null ? void 0 : p.length) ? (Object.keys(p[0]).forEach((m) => B.push({ key: m, label: ta(m) })), B) : (Array.isArray(v) && v.forEach((m) => {
      typeof m == "string" ? B.push({ key: m, label: ta(m) }) : Pt(m) && m.key && typeof m.key == "string" && B.push({ ...m });
    }), B);
  }, t = ref([]), a = (v, p, B, m) => (t.value = bt(p), "isFilterableTable" in m && m.isFilterableTable.value === true && B.filter && (t.value = u(t.value, B.filter, B.filterable)), "isSortable" in m && m.isSortable.value === true && (t.value = n(
    v,
    t.value,
    {
      key: B.sortBy,
      desc: m.sortDescBoolean.value
    },
    B.sortCompare
  )), t.value), l = ref(void 0), n = (v, p, B, m) => {
    if (!B || !B.key)
      return p;
    const $ = B.key;
    return p.sort((V, T) => {
      if (m !== void 0)
        return m(V, T, B.key, B.desc);
      const b = (C) => typeof C == "object" ? JSON.stringify(C) : C;
      return b(V[$]) > b(T[$]) ? B.desc ? -1 : 1 : b(T[$]) > b(V[$]) ? B.desc ? 1 : -1 : 0;
    });
  }, u = (v, p, B) => v.filter(
    (m) => Object.entries(m).filter(($) => {
      const [V, T] = $;
      return !T || V[0] === "_" || B.length > 0 && !B.includes(V) ? false : (typeof T == "object" ? JSON.stringify(Object.values(T)) : typeof T == "string" ? T : T.toString()).toLowerCase().includes(p.toLowerCase());
    }).length > 0
  );
  return {
    normaliseFields: e,
    mapItems: a,
    internalItems: t,
    updateInternalItems: async (v) => {
      try {
        return t.value = await Lt(v), t.value;
      } catch {
        return;
      }
    },
    filterEvent: l,
    notifyFilteredItems: () => {
      l.value && l.value(t.value);
    }
  };
};
var jr = ["title", "abbr", "onClick"];
var Gr = { class: "d-inline-flex flex-nowrap align-items-center gap-1" };
var Ur = { key: 1 };
var Wr = ["onClick", "onDblclick", "onMouseenter", "onMouseleave"];
var Kr = {
  key: 0,
  class: "b-table-stacked-label"
};
var Xr = ["colspan"];
var Jr = ["colspan"];
var Yr = { class: "d-flex align-items-center justify-content-center gap-2" };
var Zr = createBaseVNode("strong", null, "Loading...", -1);
var Qr = {
  key: 1,
  class: "b-table-empty-slot"
};
var eu = ["colspan"];
var tu = { key: 0 };
var au = ["title", "abbr", "onClick"];
var lu = { key: 1 };
var nu = { key: 2 };
var ou = { key: 3 };
var su = defineComponent({
  __name: "BTable",
  props: {
    align: null,
    caption: null,
    captionTop: { default: false },
    borderless: { default: false },
    bordered: { default: false },
    borderVariant: null,
    dark: { default: false },
    fields: { default: () => [] },
    footClone: { default: false },
    hover: { default: false },
    items: { default: () => [] },
    provider: null,
    sortCompare: null,
    noProvider: null,
    noProviderPaging: null,
    noProviderSorting: null,
    noProviderFiltering: null,
    responsive: { type: [Boolean, String], default: false },
    small: { default: false },
    striped: { default: false },
    stacked: { type: [Boolean, String], default: false },
    labelStacked: { type: Boolean, default: false },
    variant: null,
    sortBy: null,
    sortDesc: { default: false },
    sortInternal: { default: true },
    selectable: { default: false },
    stickySelect: { default: false },
    selectHead: { type: [Boolean, String], default: true },
    selectMode: { default: "single" },
    selectionVariant: { default: "primary" },
    stickyHeader: { default: false },
    busy: { default: false },
    showEmpty: { default: false },
    perPage: null,
    currentPage: { default: 1 },
    filter: null,
    filterable: null,
    emptyText: { default: "There are no records to show" },
    emptyFilteredText: { default: "There are no records matching your request" }
  },
  emits: ["headClicked", "rowClicked", "rowDblClicked", "rowHovered", "rowUnhovered", "rowSelected", "rowUnselected", "selection", "update:busy", "update:sortBy", "update:sortDesc", "sorted", "filtered"],
  setup(e, { expose: t, emit: a }) {
    const l = e, n = useSlots(), u = Mr(), f = r(toRef(l, "footClone")), c = r(toRef(l, "sortDesc")), v = r(toRef(l, "sortInternal")), p = r(toRef(l, "selectable")), B = r(toRef(l, "stickySelect")), m = r(toRef(l, "labelStacked")), $ = r(toRef(l, "busy")), V = r(toRef(l, "showEmpty")), T = r(toRef(l, "noProviderPaging")), b = r(toRef(l, "noProviderSorting")), k = r(toRef(l, "noProviderFiltering")), y = ref($.value);
    u.filterEvent.value = async (S) => {
      if (w.value) {
        await N();
        return;
      }
      const j = await Lt(S);
      a("filtered", j);
    };
    const C = ref(/* @__PURE__ */ new Set([])), q = computed(() => C.value.size > 0), E = computed(() => ({
      [`align-${l.align}`]: l.align !== void 0,
      "b-table-selectable": p.value,
      [`b-table-select-${l.selectMode}`]: p.value,
      "b-table-selecting user-select-none": p.value && q.value,
      "b-table-busy": y.value,
      "b-table-sortable": L.value,
      "b-table-sort-desc": L.value && c.value === true,
      "b-table-sort-asc": L.value && c.value === false
    })), I = computed(() => ({
      bordered: l.bordered,
      borderless: l.borderless,
      borderVariant: l.borderVariant,
      captionTop: l.captionTop,
      dark: l.dark,
      hover: l.hover,
      responsive: l.responsive,
      striped: l.striped,
      stacked: l.stacked,
      small: l.small,
      tableClass: E.value,
      tableVariant: l.variant,
      stickyHeader: l.stickyHeader
    })), _ = computed(() => u.normaliseFields(l.fields, l.items)), z = computed(
      () => _.value.length + (p.value ? 1 : 0)
    ), x = computed(() => l.filter !== void 0 && l.filter !== ""), w = computed(() => l.provider !== void 0), P = computed(
      () => p.value && (!!l.selectHead || n.selectHead !== void 0)
    ), L = computed(
      () => l.fields.filter((S) => typeof S == "string" ? false : S.sortable).length > 0
    ), te = computed(() => L.value && v.value === true), Q = computed(() => {
      const S = w.value ? u.internalItems.value : te.value ? u.mapItems(l.fields, l.items, l, {
        isSortable: L,
        isFilterableTable: x,
        sortDescBoolean: c
      }) : l.items;
      if (l.perPage !== void 0) {
        const j = (l.currentPage - 1) * l.perPage;
        return S.splice(j, l.perPage);
      }
      return S;
    }), ie = (S) => typeof S == "string" ? aa(S) : S.label !== void 0 ? S.label : typeof S.key == "string" ? aa(S.key) : S.key, K = (S, j, ue = false) => {
      const R = typeof S == "string" ? S : S.key;
      a("headClicked", R, S, j, ue), pe(S);
    }, me = (S, j, ue) => {
      a("rowClicked", S, j, ue), H(S, j, ue.shiftKey);
    }, ae = (S, j, ue) => a("rowDblClicked", S, j, ue), ge = (S, j, ue) => a("rowHovered", S, j, ue), ye = (S, j, ue) => a("rowUnhovered", S, j, ue), pe = (S) => {
      if (!L.value)
        return;
      const j = typeof S == "string" ? S : S.key, ue = typeof S == "string" ? false : S.sortable;
      if (L.value === true && ue === true) {
        const R = !c.value;
        j !== l.sortBy && a("update:sortBy", j), a("update:sortDesc", R), a("sorted", j, R);
      }
    }, re = () => {
      !p.value || a("selection", Array.from(C.value));
    }, H = (S, j, ue = false) => {
      if (!!p.value) {
        if (C.value.has(S))
          C.value.delete(S), a("rowUnselected", S);
        else if (l.selectMode === "single" && C.value.size > 0 && (C.value.forEach((R) => a("rowUnselected", R)), C.value.clear()), l.selectMode === "range" && C.value.size > 0 && ue) {
          const R = Array.from(C.value).pop(), Se = Q.value.findIndex((Me) => Me === R), de = Math.min(Se, j), _t = Math.max(Se, j);
          Q.value.slice(de, _t + 1).forEach((Me) => {
            C.value.has(Me) || (C.value.add(Me), a("rowSelected", Me));
          });
        } else
          C.value.add(S), a("rowSelected", S);
        re();
      }
    }, N = async () => {
      if (!w.value || !l.provider || y.value)
        return;
      y.value = true;
      const S = new Proxy(
        {
          currentPage: l.currentPage,
          filter: l.filter,
          sortBy: l.sortBy,
          sortDesc: l.sortDesc,
          perPage: l.perPage
        },
        {
          get(ue, R) {
            return R in ue ? ue[R] : void 0;
          },
          set() {
            return console.error("BTable provider context is a read-only object."), true;
          }
        }
      ), j = l.provider(S, u.updateInternalItems);
      if (j !== void 0) {
        if (j instanceof Promise)
          try {
            const ue = await j;
            return Array.isArray(ue) ? await u.updateInternalItems(ue) : void 0;
          } finally {
            y.value && (y.value = false);
          }
        try {
          return await u.updateInternalItems(j);
        } finally {
          y.value && (y.value = false);
        }
      }
    }, X = (S) => {
      S._showDetails = !S._showDetails;
    }, ne = (S) => [
      S.class,
      S.thClass,
      S.variant ? `table-${S.variant}` : void 0,
      {
        "b-table-sortable-column": L.value && S.sortable,
        "b-table-sticky-column": S.stickyColumn
      }
    ], Y = (S, j) => [
      S.class,
      S.tdClass,
      S.variant ? `table-${S.variant}` : void 0,
      (j == null ? void 0 : j._cellVariants) && (j == null ? void 0 : j._cellVariants[S.key]) ? `table-${j == null ? void 0 : j._cellVariants[S.key]}` : void 0,
      {
        "b-table-sticky-column": S.stickyColumn
      }
    ], se = (S) => [
      S._rowVariant ? `table-${S._rowVariant}` : null,
      S._rowVariant ? `table-${S._rowVariant}` : null,
      p.value && C.value.has(S) ? `selected table-${l.selectionVariant}` : null
    ], Te = () => {
      if (!p.value)
        return;
      const S = C.value.size > 0 ? Array.from(C.value) : [];
      C.value = /* @__PURE__ */ new Set([...Q.value]), C.value.forEach((j) => {
        S.includes(j) || a("rowSelected", j);
      }), re();
    }, Ue = () => {
      !p.value || (C.value.forEach((S) => {
        a("rowUnselected", S);
      }), C.value = /* @__PURE__ */ new Set([]), re());
    }, Tt = (S) => {
      if (!p.value)
        return;
      const j = Q.value[S];
      !j || C.value.has(j) || (C.value.add(j), a("rowSelected", j), re());
    }, st = (S) => {
      if (!p.value)
        return;
      const j = Q.value[S];
      !j || !C.value.has(j) || (C.value.delete(j), a("rowUnselected", j), re());
    }, Re = async (S, j, ue) => {
      if (j === ue)
        return;
      const R = (Nl) => l.noProvider && l.noProvider.includes(Nl), Se = !["currentPage", "perPage"].includes(S), de = ["currentPage", "perPage"].includes(S) && (R("paging") || T.value === true), _t = ["filter"].includes(S) && (R("filtering") || k.value === true), Me = ["sortBy", "sortDesc"].includes(S) && (R("sorting") || b.value === true);
      de || _t || Me || (await N(), Se && u.notifyFilteredItems());
    };
    return watch(
      () => l.filter,
      (S, j) => {
        S === j || w.value || S || Lt(l.items).then((ue) => a("filtered", ue));
      }
    ), watch(
      () => y.value,
      () => y.value !== $.value && a("update:busy", y.value)
    ), watch(
      () => $.value,
      () => y.value !== $.value && (y.value = $.value)
    ), watch(
      () => l.filter,
      (S, j) => Re("filter", S, j)
    ), watch(
      () => l.currentPage,
      (S, j) => Re("currentPage", S, j)
    ), watch(
      () => l.perPage,
      (S, j) => Re("perPage", S, j)
    ), watch(
      () => l.sortBy,
      (S, j) => Re("sortBy", S, j)
    ), watch(
      () => l.sortDesc,
      (S, j) => Re("sortDesc", S, j)
    ), onMounted(() => {
      w.value && N();
    }), t({
      selectAllRows: Te,
      clearSelected: Ue,
      selectRow: Tt,
      unselectRow: st
    }), (S, j) => (openBlock(), createBlock(wt, normalizeProps(guardReactiveProps(unref(I))), {
      default: withCtx(() => {
        var ue;
        return [
          createBaseVNode("thead", null, [
            S.$slots["thead-top"] ? renderSlot(S.$slots, "thead-top", { key: 0 }) : createCommentVNode("", true),
            createBaseVNode("tr", null, [
              unref(P) ? (openBlock(), createElementBlock("th", {
                key: 0,
                class: normalizeClass(["b-table-selection-column", {
                  "b-table-sticky-column": unref(B)
                }])
              }, [
                renderSlot(S.$slots, "select-head", {}, () => [
                  createTextVNode(toDisplayString(typeof e.selectHead == "boolean" ? "Selected" : e.selectHead), 1)
                ])
              ], 2)) : createCommentVNode("", true),
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(_), (R) => (openBlock(), createElementBlock("th", mergeProps({
                key: R.key,
                scope: "col",
                class: ne(R),
                title: R.headerTitle,
                abbr: R.headerAbbr,
                style: R.thStyle
              }, R.thAttr, {
                onClick: (Se) => K(R, Se)
              }), [
                createBaseVNode("div", Gr, [
                  renderSlot(S.$slots, "sort-icon", {
                    field: R,
                    sortBy: e.sortBy,
                    selected: R.key === e.sortBy,
                    isDesc: unref(c),
                    direction: unref(c) ? "desc" : "asc"
                  }, () => [
                    unref(L) && R.sortable ? (openBlock(), createElementBlock("span", {
                      key: 0,
                      class: normalizeClass(["b-table-sort-icon", {
                        sorted: R.key === e.sortBy,
                        [`sorted-${unref(c) ? "desc" : "asc"}`]: R.key === e.sortBy
                      }])
                    }, null, 2)) : createCommentVNode("", true)
                  ]),
                  createBaseVNode("div", null, [
                    S.$slots["head(" + R.key + ")"] || S.$slots["head()"] ? renderSlot(S.$slots, S.$slots["head(" + R.key + ")"] ? "head(" + R.key + ")" : "head()", {
                      key: 0,
                      label: R.label
                    }) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                      createTextVNode(toDisplayString(ie(R)), 1)
                    ], 64))
                  ])
                ])
              ], 16, jr))), 128))
            ]),
            S.$slots["thead-sub"] ? (openBlock(), createElementBlock("tr", Ur, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(_), (R) => (openBlock(), createElementBlock("td", {
                key: R.key,
                scope: "col",
                class: normalizeClass([R.class, R.thClass, R.variant ? `table-${R.variant}` : ""])
              }, [
                S.$slots["thead-sub"] ? renderSlot(S.$slots, "thead-sub", mergeProps({
                  key: 0,
                  items: unref(_)
                }, R)) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                  createTextVNode(toDisplayString(R.label), 1)
                ], 64))
              ], 2))), 128))
            ])) : createCommentVNode("", true)
          ]),
          createBaseVNode("tbody", null, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(Q), (R, Se) => (openBlock(), createElementBlock(Fragment, { key: Se }, [
              createBaseVNode("tr", {
                class: normalizeClass(se(R)),
                onClick: (de) => !unref(ut)(de) && me(R, Se, de),
                onDblclick: (de) => !unref(ut)(de) && ae(R, Se, de),
                onMouseenter: (de) => !unref(ut)(de) && ge(R, Se, de),
                onMouseleave: (de) => !unref(ut)(de) && ye(R, Se, de)
              }, [
                unref(P) ? (openBlock(), createElementBlock("td", {
                  key: 0,
                  class: normalizeClass(["b-table-selection-column", {
                    "b-table-sticky-column": unref(B)
                  }])
                }, [
                  renderSlot(S.$slots, "select-cell", {}, () => [
                    createBaseVNode("span", {
                      class: normalizeClass(C.value.has(R) ? "text-primary" : "")
                    }, "🗹", 2)
                  ])
                ], 2)) : createCommentVNode("", true),
                (openBlock(true), createElementBlock(Fragment, null, renderList(unref(_), (de) => (openBlock(), createElementBlock("td", mergeProps({
                  key: de.key
                }, de.tdAttr, {
                  class: Y(de, R)
                }), [
                  e.stacked && unref(m) ? (openBlock(), createElementBlock("label", Kr, toDisplayString(ie(de)), 1)) : createCommentVNode("", true),
                  S.$slots["cell(" + de.key + ")"] || S.$slots["cell()"] ? renderSlot(S.$slots, S.$slots["cell(" + de.key + ")"] ? "cell(" + de.key + ")" : "cell()", {
                    key: 1,
                    value: R[de.key],
                    index: Se,
                    item: R,
                    field: de,
                    items: e.items,
                    toggleDetails: () => X(R),
                    detailsShowing: R._showDetails
                  }) : (openBlock(), createElementBlock(Fragment, { key: 2 }, [
                    createTextVNode(toDisplayString(R[de.key]), 1)
                  ], 64))
                ], 16))), 128))
              ], 42, Wr),
              R._showDetails === true && S.$slots["row-details"] ? (openBlock(), createElementBlock("tr", {
                key: 0,
                class: normalizeClass(se(R))
              }, [
                createBaseVNode("td", { colspan: unref(z) }, [
                  renderSlot(S.$slots, "row-details", {
                    item: R,
                    toggleDetails: () => X(R)
                  })
                ], 8, Xr)
              ], 2)) : createCommentVNode("", true)
            ], 64))), 128)),
            y.value ? (openBlock(), createElementBlock("tr", {
              key: 0,
              class: normalizeClass(["b-table-busy-slot", { "b-table-static-busy": unref(Q).length == 0 }])
            }, [
              createBaseVNode("td", { colspan: unref(z) }, [
                renderSlot(S.$slots, "table-busy", {}, () => [
                  createBaseVNode("div", Yr, [
                    createVNode(Ct, { class: "align-middle" }),
                    Zr
                  ])
                ])
              ], 8, Jr)
            ], 2)) : createCommentVNode("", true),
            unref(V) && unref(Q).length === 0 ? (openBlock(), createElementBlock("tr", Qr, [
              createBaseVNode("td", { colspan: unref(z) }, [
                renderSlot(S.$slots, "empty", {
                  items: unref(Q),
                  filtered: unref(x)
                }, () => [
                  createTextVNode(toDisplayString(unref(x) ? e.emptyFilteredText : e.emptyText), 1)
                ])
              ], 8, eu)
            ])) : createCommentVNode("", true)
          ]),
          unref(f) ? (openBlock(), createElementBlock("tfoot", tu, [
            createBaseVNode("tr", null, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(unref(_), (R) => (openBlock(), createElementBlock("th", mergeProps({
                key: R.key
              }, R.thAttr, {
                scope: "col",
                class: [R.class, R.thClass, R.variant ? `table-${R.variant}` : ""],
                title: R.headerTitle,
                abbr: R.headerAbbr,
                style: R.thStyle,
                onClick: (Se) => K(R, Se, true)
              }), toDisplayString(R.label), 17, au))), 128))
            ])
          ])) : S.$slots["custom-foot"] ? (openBlock(), createElementBlock("tfoot", lu, [
            renderSlot(S.$slots, "custom-foot", {
              fields: unref(_),
              items: e.items,
              columns: (ue = unref(_)) == null ? void 0 : ue.length
            })
          ])) : createCommentVNode("", true),
          S.$slots["table-caption"] ? (openBlock(), createElementBlock("caption", nu, [
            renderSlot(S.$slots, "table-caption")
          ])) : e.caption ? (openBlock(), createElementBlock("caption", ou, toDisplayString(e.caption), 1)) : createCommentVNode("", true)
        ];
      }),
      _: 3
    }, 16));
  }
});
var iu = defineComponent({
  __name: "BTbody",
  props: {
    variant: null
  },
  setup(e) {
    const t = e, a = computed(() => ({
      [`thead-${t.variant}`]: t.variant !== void 0
    }));
    return (l, n) => (openBlock(), createElementBlock("tbody", {
      role: "rowgroup",
      class: normalizeClass(unref(a))
    }, [
      renderSlot(l.$slots, "default")
    ], 2));
  }
});
var ru = ["scope", "colspan", "rowspan", "data-label"];
var uu = { key: 0 };
var du = defineComponent({
  __name: "BTd",
  props: {
    colspan: null,
    rowspan: null,
    stackedHeading: null,
    stickyColumn: { default: false },
    variant: null
  },
  setup(e) {
    const t = e, a = r(toRef(t, "stickyColumn")), l = computed(() => ({
      [`table-${t.variant}`]: t.variant !== void 0,
      "b-table-sticky-column": a.value,
      "table-b-table-default": a.value && t.variant === void 0
    })), n = computed(() => t.colspan ? "colspan" : t.rowspan ? "rowspan" : "col");
    return (u, f) => (openBlock(), createElementBlock("td", {
      role: "cell",
      scope: unref(n),
      class: normalizeClass(unref(l)),
      colspan: e.colspan,
      rowspan: e.rowspan,
      "data-label": e.stackedHeading
    }, [
      e.stackedHeading ? (openBlock(), createElementBlock("div", uu, [
        renderSlot(u.$slots, "default")
      ])) : renderSlot(u.$slots, "default", { key: 1 })
    ], 10, ru));
  }
});
var cu = defineComponent({
  __name: "BTfoot",
  props: {
    variant: null
  },
  setup(e) {
    const t = e, a = computed(() => ({
      [`table-${t.variant}`]: t.variant !== void 0
    }));
    return (l, n) => (openBlock(), createElementBlock("tfoot", {
      role: "rowgroup",
      class: normalizeClass(unref(a))
    }, [
      renderSlot(l.$slots, "default")
    ], 2));
  }
});
var fu = ["scope", "colspan", "rowspan", "data-label"];
var vu = { key: 0 };
var mu = defineComponent({
  __name: "BTh",
  props: {
    colspan: null,
    rowspan: null,
    stackedHeading: null,
    stickyColumn: { default: false },
    variant: null
  },
  setup(e) {
    const t = e, a = r(toRef(t, "stickyColumn")), l = computed(() => ({
      [`table-${t.variant}`]: t.variant !== void 0,
      "b-table-sticky-column": a.value,
      "table-b-table-default": a.value && t.variant === void 0
    })), n = computed(() => t.colspan ? "colspan" : t.rowspan ? "rowspan" : "col");
    return (u, f) => (openBlock(), createElementBlock("th", {
      role: "columnheader",
      scope: unref(n),
      class: normalizeClass(unref(l)),
      colspan: e.colspan,
      rowspan: e.rowspan,
      "data-label": e.stackedHeading
    }, [
      e.stackedHeading !== void 0 ? (openBlock(), createElementBlock("div", vu, [
        renderSlot(u.$slots, "default")
      ])) : renderSlot(u.$slots, "default", { key: 1 })
    ], 10, fu));
  }
});
var bu = defineComponent({
  __name: "BThead",
  props: {
    variant: null
  },
  setup(e) {
    const t = e, a = computed(() => ({
      [`table-${t.variant}`]: t.variant !== void 0
    }));
    return (l, n) => (openBlock(), createElementBlock("thead", {
      role: "rowgroup",
      class: normalizeClass(unref(a))
    }, [
      renderSlot(l.$slots, "default")
    ], 2));
  }
});
var gu = defineComponent({
  __name: "BTr",
  props: {
    variant: null
  },
  setup(e) {
    const t = e, a = computed(() => ({
      [`table-${t.variant}`]: t.variant !== void 0
    }));
    return (l, n) => (openBlock(), createElementBlock("tr", {
      role: "row",
      class: normalizeClass(unref(a))
    }, [
      renderSlot(l.$slots, "default")
    ], 2));
  }
});
var pu = ["id", "data-bs-target", "aria-controls", "aria-selected", "onClick"];
var zl = Symbol();
var hu = defineComponent({
  __name: "BTabs",
  props: {
    activeNavItemClass: null,
    activeTabClass: null,
    align: null,
    card: { default: false },
    contentClass: null,
    end: { default: false },
    fill: { default: false },
    id: null,
    justified: { default: false },
    lazy: { default: false },
    navClass: null,
    navWrapperClass: null,
    noFade: { default: false },
    noNavStyle: { default: false },
    pills: { default: false },
    small: { default: false },
    tag: { default: "div" },
    vertical: { default: false },
    modelValue: { default: -1 }
  },
  emits: ["update:modelValue", "activate-tab", "click"],
  setup(e, { emit: t }) {
    const a = e, l = useSlots(), n = r(toRef(a, "card")), u = r(toRef(a, "end")), f = r(toRef(a, "fill")), c = r(toRef(a, "justified")), v = r(toRef(a, "lazy")), p = r(toRef(a, "noFade")), B = r(toRef(a, "noNavStyle")), m = r(toRef(a, "pills")), $ = r(toRef(a, "small")), V = r(toRef(a, "vertical")), T = ref(a.modelValue), b = ref(""), k = computed({
      get: () => T.value,
      set: (w) => {
        T.value = w, y.value.length > 0 && w >= 0 && w < y.value.length ? b.value = y.value[w].buttonId : b.value = "", t("update:modelValue", w);
      }
    }), y = computed(() => {
      let w = [];
      return l.default && (w = x(l).map((P, L) => {
        P.props || (P.props = {});
        const te = P.props["button-id"] || Ee("tab"), Q = P.props.id || Ee(), ie = k.value > -1 ? L === k.value : P.props.active === "", K = P.props["title-item-class"], me = P.props["title-link-attributes"];
        return {
          buttonId: te,
          contentId: Q,
          active: ie,
          disabled: P.props.disabled === "" || P.props.disabled === true,
          navItemClasses: [
            {
              active: ie,
              disabled: P.props.disabled === "" || P.props.disabled === true
            },
            ie && a.activeNavItemClass ? a.activeNavItemClass : null,
            P.props["title-link-class"]
          ],
          tabClasses: [
            {
              fade: !p.value
            },
            ie && a.activeTabClass ? a.activeTabClass : null
          ],
          target: `#${Q}`,
          title: P.props.title,
          titleItemClass: K,
          titleLinkAttributes: me,
          onClick: P.props.onClick,
          tab: P,
          tabComponent: () => x(l)[L]
        };
      })), w;
    }), C = computed(() => !((y == null ? void 0 : y.value) && y.value.length > 0)), q = computed(() => ({
      "d-flex": V.value,
      "align-items-start": V.value
    })), E = ot(toRef(a, "align")), I = computed(() => ({
      "nav-pills": m.value,
      "flex-column me-3": V.value,
      [E.value]: a.align !== void 0,
      "nav-fill": f.value,
      "card-header-tabs": n.value,
      "nav-justified": c.value,
      "nav-tabs": !B.value && !m.value,
      small: $.value
    })), _ = (w) => {
      let P = false;
      if (w !== void 0 && w > -1 && w < y.value.length && !y.value[w].disabled && (k.value < 0 || y.value[w].buttonId !== b.value)) {
        const L = new Xe("activate-tab", { cancelable: true });
        t("activate-tab", w, k.value, L), L.defaultPrevented || (k.value = w, P = true);
      }
      return !P && a.modelValue !== k.value && t("update:modelValue", k.value), P;
    }, z = (w, P) => {
      var L;
      _(P), P >= 0 && !y.value[P].disabled && ((L = y.value[P]) == null ? void 0 : L.onClick) && typeof y.value[P].onClick == "function" && y.value[P].onClick(w);
    }, x = (w) => !w || !w.default ? [] : w.default().reduce((P, L) => (typeof L.type == "symbol" ? P = P.concat(L.children) : P.push(L), P), []).filter((P) => {
      var L;
      return ((L = P.type) == null ? void 0 : L.__name) === "BTab";
    });
    return _(T.value), watch(
      () => a.modelValue,
      (w, P) => {
        if (w === P)
          return;
        if (w = Math.max(w, -1), P = Math.max(P, -1), y.value.length <= 0) {
          k.value = -1;
          return;
        }
        const L = w > P;
        let te = w;
        const Q = y.value.length - 1;
        for (; te >= 0 && te <= Q && y.value[te].disabled; )
          te += L ? 1 : -1;
        if (te < 0) {
          _(0);
          return;
        }
        if (te >= y.value.length) {
          _(y.value.length - 1);
          return;
        }
        _(te);
      }
    ), watch(
      () => y.value,
      () => {
        let w = y.value.map((P) => P.active && !P.disabled).lastIndexOf(true);
        w < 0 && (k.value >= y.value.length ? w = y.value.map((P) => !P.disabled).lastIndexOf(true) : y.value[k.value] && !y.value[k.value].disabled && (w = k.value)), w < 0 && (w = y.value.map((P) => !P.disabled).indexOf(true)), y.value.forEach((P, L) => P.active = L === w), _(w);
      }
    ), onMounted(() => {
      if (k.value < 0 && y.value.length > 0 && !y.value.some((w) => w.active)) {
        const w = y.value.map((P) => !P.disabled).indexOf(true);
        _(w >= 0 ? w : -1);
      }
    }), provide(zl, {
      lazy: v.value,
      card: n.value
    }), (w, P) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: e.id,
      class: normalizeClass(["tabs", unref(q)])
    }, {
      default: withCtx(() => [
        unref(u) ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(["tab-content", e.contentClass])
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(y), ({ tabComponent: L, contentId: te, tabClasses: Q, active: ie }, K) => (openBlock(), createBlock(resolveDynamicComponent(L()), {
            id: te,
            key: K,
            class: normalizeClass(Q),
            active: ie
          }, null, 8, ["id", "class", "active"]))), 128)),
          unref(C) ? (openBlock(), createElementBlock("div", {
            key: "bv-empty-tab",
            class: normalizeClass(["tab-pane active", { "card-body": unref(n) }])
          }, [
            renderSlot(w.$slots, "empty")
          ], 2)) : createCommentVNode("", true)
        ], 2)) : createCommentVNode("", true),
        createBaseVNode("div", {
          class: normalizeClass([e.navWrapperClass, { "card-header": unref(n), "ms-auto": e.vertical && unref(u) }])
        }, [
          createBaseVNode("ul", {
            class: normalizeClass(["nav", [unref(I), e.navClass]]),
            role: "tablist"
          }, [
            renderSlot(w.$slots, "tabs-start"),
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(y), ({ tab: L, buttonId: te, contentId: Q, navItemClasses: ie, active: K, target: me }, ae) => (openBlock(), createElementBlock("li", {
              key: ae,
              class: normalizeClass(["nav-item", L.props["title-item-class"]])
            }, [
              createBaseVNode("button", mergeProps({
                id: te,
                class: ["nav-link", ie],
                "data-bs-toggle": "tab",
                "data-bs-target": me,
                role: "tab",
                "aria-controls": Q,
                "aria-selected": K
              }, L.props["title-link-attributes"], {
                onClick: withModifiers((ge) => z(ge, ae), ["stop", "prevent"])
              }), [
                L.children && L.children.title ? (openBlock(), createBlock(resolveDynamicComponent(L.children.title), { key: 0 })) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                  createTextVNode(toDisplayString(L.props.title), 1)
                ], 64))
              ], 16, pu)
            ], 2))), 128)),
            renderSlot(w.$slots, "tabs-end")
          ], 2)
        ], 2),
        unref(u) ? createCommentVNode("", true) : (openBlock(), createElementBlock("div", {
          key: 1,
          class: normalizeClass(["tab-content", e.contentClass])
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(y), ({ tabComponent: L, contentId: te, tabClasses: Q, active: ie }, K) => (openBlock(), createBlock(resolveDynamicComponent(L()), {
            id: te,
            key: K,
            class: normalizeClass(Q),
            active: ie
          }, null, 8, ["id", "class", "active"]))), 128)),
          unref(C) ? (openBlock(), createElementBlock("div", {
            key: "bv-empty-tab",
            class: normalizeClass(["tab-pane active", { "card-body": unref(n) }])
          }, [
            renderSlot(w.$slots, "empty")
          ], 2)) : createCommentVNode("", true)
        ], 2))
      ]),
      _: 3
    }, 8, ["id", "class"]));
  }
});
var yu = defineComponent({
  __name: "BTab",
  props: {
    id: null,
    title: null,
    active: { default: false },
    buttonId: { default: void 0 },
    disabled: { default: false },
    lazy: { default: void 0 },
    lazyOnce: { default: void 0 },
    noBody: { type: [Boolean, String], default: false },
    tag: { default: "div" },
    titleItemClass: null,
    titleLinkAttributes: { default: void 0 },
    titleLinkClass: null
  },
  setup(e) {
    const t = e, a = inject(zl, null), l = r(toRef(t, "active")), n = r(toRef(t, "disabled")), u = r(toRef(t, t.lazyOnce !== void 0 ? "lazyOnce" : "lazy")), f = ref(false), c = computed(() => !!((a == null ? void 0 : a.lazy) || u.value)), v = computed(() => t.lazyOnce !== void 0), p = computed(() => l.value && !n.value), B = computed(() => {
      const $ = c.value && v.value && f.value;
      return p.value || !c.value || $;
    }), m = computed(() => ({
      active: l.value,
      show: l.value,
      "card-body": (a == null ? void 0 : a.card) && t.noBody === false
    }));
    return watch(
      () => B.value,
      ($) => {
        $ && !f.value && (f.value = true);
      }
    ), ($, V) => (openBlock(), createBlock(resolveDynamicComponent(e.tag), {
      id: e.id,
      class: normalizeClass(["tab-pane", unref(m)]),
      role: "tabpanel",
      "aria-labelledby": "profile-tab"
    }, {
      default: withCtx(() => [
        unref(B) ? renderSlot($.$slots, "default", { key: 0 }) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["id", "class"]));
  }
});
var Bu = {
  BAccordion: Nn,
  BAccordionItem: eo,
  BNavText: Wi,
  BAlert: ao,
  BAvatar: io,
  BAvatarGroup: lo,
  BNavForm: Di,
  BBadge: vo,
  BBreadcrumb: ho,
  BBreadcrumbItem: sl,
  BButton: lt,
  BButtonGroup: ko,
  BButtonToolbar: So,
  BCard: vl,
  BCardBody: cl,
  BCardFooter: fl,
  BCardGroup: To,
  BCardHeader: rl,
  BCardImg: pt,
  BCardSubtitle: dl,
  BCardText: _o,
  BCardTitle: ul,
  BCarousel: Eo,
  BCarouselSlide: jo,
  BCloseButton: Ze,
  BCol: et,
  BCollapse: ll,
  BContainer: Zo,
  BDropdown: hl,
  BDropdownDivider: as,
  BDropdownForm: is,
  BDropdownGroup: cs,
  BDropdownHeader: bs,
  BDropdownItem: ps,
  BDropdownItemButton: Bs,
  BDropdownText: Cs,
  BForm: yl,
  BFormCheckbox: Bl,
  BFormCheckboxGroup: Os,
  BFormFloatingLabel: _s,
  BFormGroup: qs,
  BFormInput: Us,
  BFormInvalidFeedback: Dt,
  BFormRadio: kl,
  BFormRadioGroup: Zs,
  BFormRow: ft,
  BFormSelect: ai,
  BFormSelectOption: Qt,
  BFormSelectOptionGroup: Cl,
  BFormText: Ht,
  BFormTextarea: $i,
  BFormTag: Sl,
  BFormTags: pi,
  BFormValidFeedback: Rt,
  BImg: Zt,
  BInputGroup: Vi,
  BInputGroupAddon: ea,
  BInputGroupAppend: Ai,
  BInputGroupPrepend: xi,
  BInputGroupText: wl,
  BLink: Ae,
  BListGroup: Ii,
  BListGroupItem: Fi,
  BModal: Ni,
  BNav: Ei,
  BNavbar: Ki,
  BNavbarBrand: Yi,
  BNavbarNav: Zi,
  BNavbarToggle: er,
  BNavItem: Mi,
  BNavItemDropdown: Gi,
  BOffcanvas: sr,
  BOverlay: ir,
  BPagination: gr,
  BPlaceholder: Ie,
  BPlaceholderButton: Al,
  BPlaceholderCard: pr,
  BPlaceholderTable: hr,
  BPlaceholderWrapper: yr,
  BPopover: wr,
  BProgress: _r,
  BProgressBar: xl,
  BRow: xr,
  BSkeleton: vt,
  BSkeletonIcon: Ir,
  BSkeletonTable: Or,
  BSkeletonWrapper: Lr,
  BSpinner: Ct,
  BFormSpinButton: Hr,
  BTab: yu,
  BTable: su,
  BTableSimple: wt,
  BTbody: iu,
  BTd: du,
  BTfoot: cu,
  BTh: mu,
  BThead: bu,
  BTr: gu,
  BToast: pl,
  BToaster: Et,
  BToastContainer: Et,
  BTabs: hu,
  BTransition: St,
  BToastPlugin: Jo
};
var Su = {
  install(e, t = {}) {
    Object.entries(Bu).forEach(([a, l]) => {
      e.component(a, l);
    }), Object.entries(Xn).forEach(([a, l]) => {
      e.directive(a, l);
    }), On(e);
  }
};
export {
  Nn as BAccordion,
  eo as BAccordionItem,
  ao as BAlert,
  io as BAvatar,
  lo as BAvatarGroup,
  vo as BBadge,
  ho as BBreadcrumb,
  sl as BBreadcrumbItem,
  lt as BButton,
  ko as BButtonGroup,
  So as BButtonToolbar,
  vl as BCard,
  cl as BCardBody,
  fl as BCardFooter,
  To as BCardGroup,
  rl as BCardHeader,
  pt as BCardImg,
  dl as BCardSubtitle,
  _o as BCardText,
  ul as BCardTitle,
  Eo as BCarousel,
  jo as BCarouselSlide,
  Ze as BCloseButton,
  et as BCol,
  ll as BCollapse,
  Zo as BContainer,
  hl as BDropdown,
  as as BDropdownDivider,
  is as BDropdownForm,
  cs as BDropdownGroup,
  bs as BDropdownHeader,
  ps as BDropdownItem,
  Bs as BDropdownItemButton,
  Cs as BDropdownText,
  yl as BForm,
  Bl as BFormCheckbox,
  Os as BFormCheckboxGroup,
  _s as BFormFloatingLabel,
  qs as BFormGroup,
  Us as BFormInput,
  Dt as BFormInvalidFeedback,
  kl as BFormRadio,
  Zs as BFormRadioGroup,
  ft as BFormRow,
  ai as BFormSelect,
  Qt as BFormSelectOption,
  Cl as BFormSelectOptionGroup,
  Hr as BFormSpinButton,
  Sl as BFormTag,
  pi as BFormTags,
  Ht as BFormText,
  $i as BFormTextarea,
  Rt as BFormValidFeedback,
  Zt as BImg,
  Vi as BInputGroup,
  ea as BInputGroupAddon,
  Ai as BInputGroupAppend,
  xi as BInputGroupPrepend,
  wl as BInputGroupText,
  Ae as BLink,
  Ii as BListGroup,
  Fi as BListGroupItem,
  Ni as BModal,
  Ei as BNav,
  Di as BNavForm,
  Mi as BNavItem,
  Gi as BNavItemDropdown,
  Wi as BNavText,
  Ki as BNavbar,
  Yi as BNavbarBrand,
  Zi as BNavbarNav,
  er as BNavbarToggle,
  sr as BOffcanvas,
  ir as BOverlay,
  gr as BPagination,
  Ie as BPlaceholder,
  Al as BPlaceholderButton,
  pr as BPlaceholderCard,
  hr as BPlaceholderTable,
  yr as BPlaceholderWrapper,
  wr as BPopover,
  _r as BProgress,
  xl as BProgressBar,
  xr as BRow,
  vt as BSkeleton,
  Ir as BSkeletonIcon,
  Or as BSkeletonTable,
  Lr as BSkeletonWrapper,
  Ct as BSpinner,
  yu as BTab,
  su as BTable,
  wt as BTableSimple,
  hu as BTabs,
  iu as BTbody,
  du as BTd,
  cu as BTfoot,
  mu as BTh,
  bu as BThead,
  pl as BToast,
  Et as BToastContainer,
  Jo as BToastPlugin,
  Et as BToaster,
  gu as BTr,
  St as BTransition,
  Su as BootstrapVue3,
  Xe as BvEvent,
  sn as BvModalEvent,
  Su as default,
  Ln as useBreadcrumb,
  gl as useToast,
  Dn as vBPopover,
  Yt as vBToggle,
  Gn as vBTooltip,
  Un as vBVisible
};
//# sourceMappingURL=bootstrap-vue-3.js.map
